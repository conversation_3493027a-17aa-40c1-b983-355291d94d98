<template>
  <Body
    class="antialiased duration-300 transition-colors text-gray-800 bg-white"
  >
    <VitePwaManifest />
    <NuxtLayout>
      <NuxtLoadingIndicator />
      <NuxtPage />
    </NuxtLayout>

    <!-- Global Session Expired Modal -->
    <SessionExpiredModal
      :is-visible="isSessionExpiredModalVisible"
      :countdown-seconds="sessionExpiredCountdown"
      :allow-stay-here="false"
      @login-now="handleLoginNow"
      @time-expired="handleTimeExpired"
    />
  </Body>
</template>

<script setup>
// Components
const SessionExpiredModal = defineAsyncComponent(() =>
  import("~/components/Modal/SessionExpiredModal.vue")
);

// Session expired modal management
const {
  isSessionExpiredModalVisible,
  sessionExpiredCountdown,
  handleLoginNow,
  handleTimeExpired,
} = useSessionExpired();
</script>
