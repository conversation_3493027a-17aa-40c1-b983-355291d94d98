<template>
  <tbody>
    <tr
      v-for="n in itemCount"
      :key="n"
      class="even:bg-gray-50 odd:bg-white animate-pulse"
    >
      <!-- <PERSON><PERSON> (w-1/12) -->
      <td class="p-3 w-1/12 text-center">
        <div class="h-4 bg-gray-200 rounded w-16 mx-auto"></div>
      </td>

      <!-- <PERSON><PERSON><PERSON><PERSON> hàng (w-2/12) -->
      <td class="p-3 w-2/12">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-24"></div>
          <div class="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </td>

      <!-- Nh<PERSON> viên (w-2/12) -->
      <td class="p-3 w-2/12">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-20"></div>
          <div class="h-3 bg-gray-200 rounded w-16"></div>
        </div>
      </td>

      <!-- <PERSON><PERSON><PERSON> phẩm (w-4/12) -->
      <td class="p-3 w-4/12 text-center">
        <div class="space-y-2">
          <div class="h-3 bg-gray-200 rounded w-full"></div>
          <div class="h-3 bg-gray-200 rounded w-3/4 mx-auto"></div>
          <div class="h-3 bg-gray-200 rounded w-1/2 mx-auto"></div>
        </div>
      </td>

      <!-- Tổng giảm (w-1/12) -->
      <td class="p-3 w-1/12">
        <div class="h-4 bg-gray-200 rounded w-16"></div>
      </td>

      <!-- Thanh toán (w-2/12) -->
      <td class="p-3 w-2/12">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-20"></div>
          <div class="h-3 bg-gray-200 rounded w-16"></div>
        </div>
      </td>

      <!-- Actions (w-auto) -->
      <td class="p-3 w-auto text-center">
        <div class="h-6 w-6 bg-gray-200 rounded mx-auto"></div>
      </td>
    </tr>
  </tbody>
</template>

<script setup lang="ts">
interface Props {
  itemCount?: number;
}

withDefaults(defineProps<Props>(), {
  itemCount: 5,
});
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
