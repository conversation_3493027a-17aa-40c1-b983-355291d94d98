<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg max-w-4xl w-full animate-popup overflow-hidden"
    >
      <!-- Header -->
      <div
        class="bg-primary text-white px-4 py-2 rounded-t-lg flex items-center justify-between"
      >
        <div class="flex items-center gap-3">
          <div
            class="flex items-center justify-center w-8 h-8 bg-white/20 rounded-full"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              class="w-5 h-5"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z"
              />
            </svg>
          </div>
          <div>
            <h2 class="text-xl font-bold">Mã giảm giá</h2>
            <p class="text-white/80 text-sm">Thông tin voucher khả dụng</p>
          </div>
        </div>

        <!-- Close Button -->
        <button
          @click="cancel"
          class="flex items-center justify-center w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-200"
          title="Đóng"
          aria-label="Đóng dialog"
          type="button"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            class="w-5 h-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
      <!-- Phần nội dung -->
      <div class="max-h-[70vh] overflow-y-auto p-4">
        <div v-for="campaign in campaigns">
          <ListCampaign
            :campaign="campaign"
            :dataVoucher="dataVoucher"
            @get-campaign="handleSuggestVoucher"
          ></ListCampaign>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["confirm", "cancel"]);
const { suggestVoucher } = useCampaign();
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const orderStore = useOrderStore();
const campaigns = computed(() =>
  orderStore?.campaign?.filter((campaign: any) =>
    [
      "PROMOTION_VOUCHER",
      "PROMOTION_BIRTH_DAY",
      "PROMOTION_POINT_TO_VOUCHER",
    ].includes(campaign.type)
  )
);

const dataVoucher = ref([]);
const customer = computed(() => orderStore.customerInOrder);

const handleSuggestVoucher = async () => {
  try {
    const response = await suggestVoucher(customer.value?.id, "", "", true);
    dataVoucher.value = response?.content;
    return response;
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await handleSuggestVoucher();
});
</script>

<style scoped></style>
