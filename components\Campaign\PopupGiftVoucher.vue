<template>
  <!-- Backdrop with improved animation -->
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isVisible"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4"
        @click.self="cancel"
      >
        <!-- Modal with enhanced styling -->
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 scale-95 translate-y-4"
          enter-to-class="opacity-100 scale-100 translate-y-0"
          leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 scale-100 translate-y-0"
          leave-to-class="opacity-0 scale-95 translate-y-4"
        >
          <div
            v-if="isVisible"
            class="bg-white rounded-2xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden transform transition-all duration-300"
            @click.stop
          >
            <!-- Header with enhanced gradient using primary colors -->
            <div
              class="bg-gradient-to-r from-primary-dark to-primary-light px-4 py-3 sm:px-6 sm:py-4"
            >
              <div class="flex items-center justify-between">
                <!-- Enhanced header with icon and stats -->
                <div class="flex items-center gap-3">
                  <div
                    class="flex items-center justify-center w-10 h-10 bg-white/20 rounded-xl backdrop-blur-sm"
                  >
                    <svg
                      class="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
                      />
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-lg sm:text-xl font-bold text-white">
                      Sản phẩm quà tặng
                    </h2>
                    <p class="text-white/80 text-sm">
                      {{ totalGiftProducts }} sản phẩm có sẵn
                    </p>
                  </div>
                </div>

                <!-- Enhanced close button -->
                <button
                  @click="cancel"
                  class="flex items-center justify-center w-10 h-10 text-white/80 hover:text-white hover:bg-white/20 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50"
                  aria-label="Đóng popup"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Content section -->
            <div class="flex flex-col h-[50vh] sm:h-[55vh]">
              <!-- Loading state -->
              <div v-if="isLoading" class="flex-1 p-6">
                <div class="space-y-4">
                  <div
                    v-for="index in 3"
                    :key="`loading-${index}`"
                    class="animate-pulse"
                  >
                    <div class="bg-gray-200 rounded-lg h-24 mb-4"></div>
                    <div class="space-y-2">
                      <div class="bg-gray-200 rounded h-16"></div>
                      <div class="bg-gray-200 rounded h-16"></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Content with products -->
              <div
                v-else-if="productGift?.length"
                class="flex-1 overflow-hidden"
              >
                <!-- Products list -->
                <div class="flex-1 overflow-y-auto p-4 space-y-6">
                  <div
                    v-for="product in filteredProducts"
                    :key="product.id"
                    class="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200"
                  >
                    <!-- Campaign header -->
                    <div
                      class="px-4 py-3 border-b border-gray-100 bg-gradient-to-r from-primary-light/10 to-primary-dark/10"
                    >
                      <div class="flex items-center gap-2">
                        <svg
                          class="w-4 h-4 text-primary-dark"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
                          />
                        </svg>
                        <h3 class="font-semibold text-gray-900 text-sm">
                          {{
                            product?.campaignActionName || "Chiến dịch quà tặng"
                          }}
                        </h3>
                        <span
                          class="ml-auto text-xs px-2 py-1 bg-primary-light/20 text-primary-dark rounded-full"
                        >
                          {{ product.giftPromotions?.length || 0 }} sản phẩm
                        </span>
                      </div>
                    </div>

                    <!-- Gift products grid -->
                    <div class="p-4">
                      <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <ItemCampaignProductGiftSelectable
                          v-for="item in product.giftPromotions"
                          :key="item?.toProductId || Math.random()"
                          :product="item"
                          :productGift="product"
                          :selected="
                            selectedGiftProduct?.product_id ===
                            item?.toProductId
                          "
                          @select="handleSelectGiftProduct"
                          class="transition-all duration-200 hover:shadow-sm cursor-pointer rounded-lg"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Empty state -->
              <div v-else class="flex-1 flex items-center justify-center p-8">
                <div class="text-center max-w-sm">
                  <div
                    class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"
                  >
                    <svg
                      class="w-10 h-10 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
                      />
                    </svg>
                  </div>
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    Chưa có sản phẩm quà tặng
                  </h3>
                  <p class="text-gray-500 text-sm">
                    Hiện tại chưa có sản phẩm quà tặng nào khả dụng. Vui lòng
                    thử lại sau.
                  </p>
                </div>
              </div>

              <!-- Footer actions -->
              <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-end">
                  <div class="flex gap-3">
                    <button
                      @click="cancel"
                      class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors duration-200"
                    >
                      Hủy
                    </button>
                    <button
                      @click="confirmSelection"
                      :disabled="!selectedGiftProduct"
                      :class="[
                        'px-6 py-2 rounded-lg font-medium transition-all duration-200',
                        selectedGiftProduct
                          ? 'bg-gradient-to-r from-primary-dark to-primary-light hover:from-primary-dark/90 hover:to-primary-light/90 text-white shadow-md hover:shadow-lg'
                          : 'bg-gray-200 text-gray-400 cursor-not-allowed',
                      ]"
                    >
                      Xác nhận chọn
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
  </Teleport>
</template>

<script setup lang="ts">
// Types
interface GiftProduct {
  toProductId: string;
  productName?: string;
  sku?: string;
  price?: number;
  product_id?: string;
}

interface ProductGift {
  id: string;
  campaignActionName?: string;
  giftPromotions?: GiftProduct[];
}

interface Props {
  productGift?: ProductGift[];
  isLoading?: boolean;
}

// Props and emits
const props = withDefaults(defineProps<Props>(), {
  productGift: () => [],
  isLoading: false,
});

const emit = defineEmits<{
  confirm: [product?: GiftProduct];
  cancel: [];
}>();

// State
const isVisible = ref(true);
const selectedGiftProduct = ref<any | null>(null);
const searchQuery = ref("");

// Computed properties
const totalGiftProducts = computed(() => {
  return (
    props.productGift?.reduce((total, product) => {
      return total + (product.giftPromotions?.length || 0);
    }, 0) || 0
  );
});

const filteredProducts = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.productGift || [];
  }

  const query = searchQuery.value.toLowerCase().trim();
  return (
    props.productGift?.filter((product) => {
      // Search in campaign name
      const campaignMatch = product.campaignActionName
        ?.toLowerCase()
        .includes(query);

      // Search in gift products
      const giftMatch = product.giftPromotions?.some(
        (gift) =>
          gift.productName?.toLowerCase().includes(query) ||
          gift.sku?.toLowerCase().includes(query)
      );

      return campaignMatch || giftMatch;
    }) || []
  );
});

// Methods
const handleSelectGiftProduct = (data: any) => {
  selectedGiftProduct.value = data;
};
const { addOrderLineItems } = useOrder();
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const isLoading = ref(false);
const confirmSelection = async () => {
  if (selectedGiftProduct.value) {
    isLoading.value = true;
    const dataRequest = [
      {
        quantity: selectedGiftProduct.value?.quantity,
        gift: true,
        product_id: selectedGiftProduct.value?.product_id,
        gift_condition_product_id:
          selectedGiftProduct.value?.gift_condition_product_id,
        gift_campaign_id: selectedGiftProduct.value?.gift_campaign_id,
        gift_campaign_action_id:
          selectedGiftProduct.value?.gift_campaign_action_id,
      },
    ];
    await orderStore.addProductGiftToOrder(
      orderDetail.value?.id,
      dataRequest,
      selectedGiftProduct.value?.fromQuantity
    );
    await orderStore.updateOrder(orderDetail.value.id);
    emit("confirm");
    isVisible.value = false;
    isLoading.value = false;
  }
};

const cancel = () => {
  isVisible.value = false;
  setTimeout(() => {
    emit("cancel");
  }, 200);
};

// Keyboard event handling
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Escape") {
    cancel();
  } else if (event.key === "Enter" && selectedGiftProduct.value) {
    confirmSelection();
  }
};

// Lifecycle
onMounted(() => {
  console.log("orderDetail", orderDetail.value?.activeOrderItemProfiles);
  console.log("'props'", props.productGift);
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>

<style scoped>
/* Enhanced scrollbar styling */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Shimmer effect for loading state */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse .bg-gray-200 {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Staggered animation for loading items */
.animate-pulse:nth-child(1) {
  animation-delay: 0ms;
}
.animate-pulse:nth-child(2) {
  animation-delay: 150ms;
}
.animate-pulse:nth-child(3) {
  animation-delay: 300ms;
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #0d47a1; /* primary-dark color */
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    grid-template-columns: 1fr;
  }

  .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .text-lg {
    font-size: 1rem;
  }
}

/* Hover effects */
.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Improved input focus */
input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
}
</style>
