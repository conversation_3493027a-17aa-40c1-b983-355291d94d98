<template>
  <!-- Tree indentation and expansion controls -->
  <td class="py-2 relative">
    <div
      class="flex items-center"
      :style="{ paddingLeft: `${node.level * 20}px` }"
    >
      <!-- Tree lines -->
      <div
        v-if="node.level > 0"
        class="absolute left-0 top-0 bottom-0 w-px bg-gray-300"
        :style="{ left: `${(node.level - 1) * 20 + 10}px` }"
      ></div>

      <!-- Horizontal line for child items -->
      <div
        v-if="node.level > 0"
        class="absolute w-2 h-px bg-gray-300 top-1/2"
        :style="{ left: `${(node.level - 1) * 20 + 10}px` }"
      ></div>

      <!-- Expansion toggle button -->
      <button
        v-if="node.children.length > 0"
        @click="$emit('toggle-expansion', node.id)"
        class="w-4 h-4 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors mr-1"
      >
        <svg
          class="w-3 h-3 transition-transform"
          :class="{ 'rotate-90': node.isExpanded }"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>

      <!-- Spacer for items without children -->
      <div v-else class="w-5"></div>

      <!-- Type indicator -->
      <div class="flex items-center gap-1">
        <!-- Checkbox (following ItemTableCard pattern) -->
        <input
          v-if="!isDisable"
          type="checkbox"
          v-model="isChecked"
          @click="toogleCheckBox"
          class="w-[15px] h-[15px] accent-primary rounded-full cursor-pointer"
          :disabled="
            isDisable ||
            orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
              'INVOICE_PUBLISHED'
          "
        />
      </div>
    </div>
  </td>

  <!-- Delegate the rest to ItemTableCard with modifications -->
  <ItemTableCard
    :product="node.data"
    :isPageSale="isPageSale"
    :isDisable="isDisable || node.isDisabled"
    :isTreeNode="true"
    :nodeType="node.type"
    :nodeLevel="node.level"
    :isProductDisabled="node.isDisabled"
    class="tree-row-content"
  />
</template>

<script setup lang="ts">
import type { TreeNode } from "~/utils/productTreeHelper";
import ItemTableCard from "./ItemTableCard.vue";

const props = defineProps<{
  node: TreeNode;
  isPageSale?: boolean;
  isDisable?: boolean;
}>();

const emit = defineEmits<{
  "toggle-expansion": [nodeId: string];
}>();

const orderStore = useOrderStore();
const { disableOrderItem, enableProductDiary } = useOrder();

const orderDetail = computed(() => orderStore.orderDetail);

// Set isChecked based on orderStatus: false if status is 90, true otherwise
// Use computed to make it reactive to data changes
const isChecked = computed({
  get: () => {
    const orderStatus = props.node.data?.orderLineItem?.orderStatus;
    const isEnabled = orderStatus !== 90;

    // Debug logging
    if (process.dev) {
      console.log(
        `[ProductTreeRow] Node ${props.node.id} (${props.node.type}):`,
        {
          orderStatus,
          isEnabled,
          nodeData: props.node.data?.orderLineItem,
        }
      );
    }

    return isEnabled;
  },
  set: (_value: boolean) => {
    // This will be handled by the toggle function
  },
});

const toogleCheckBox = async () => {
  try {
    if (isChecked.value) {
      // Disable current item
      const res = await disableOrderItem(
        orderDetail.value.id,
        props.node.data?.orderLineItem?.id
      );

      // Disable all children synchronously
      if (props.node?.children?.length > 0) {
        const childPromises = props.node.children.map((child) =>
          disableOrderItem(orderDetail.value.id, child.data?.orderLineItem?.id)
        );
        await Promise.all(childPromises);
      }

      if (res?.status === 1) {
        await orderStore.getOrderById(orderDetail.value.id);
      }
    } else {
      // Enable current item
      const res = await enableProductDiary(
        orderDetail.value.id,
        props.node.data?.orderLineItem?.id
      );

      // Enable all children synchronously
      if (props.node?.children?.length > 0) {
        const childPromises = props.node.children.map((child) =>
          enableProductDiary(
            orderDetail.value.id,
            child.data?.orderLineItem?.id
          )
        );
        await Promise.all(childPromises);
      }

      if (res?.status === 1) {
        await orderStore.getOrderById(orderDetail.value.id);
      }
    }
  } catch (error) {
    console.error("Error toggling checkbox:", error);
    // Refresh data to ensure UI is in sync
    await orderStore.getOrderById(orderDetail.value.id);
  }
};
</script>

<style scoped>
/* Remove the first cell since we handle it in this component */
.tree-row-content :deep(td:first-child) {
  display: none;
}
</style>
