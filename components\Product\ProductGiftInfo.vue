<template>
  <div v-if="dataProductGift?.length > 0" class="mt-2 space-y-2">
    <div
      v-for="(campaign, campaignIndex) in dataProductGift"
      :key="campaign?.campaignActionId || campaignIndex"
      class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100 rounded-lg p-2"
    >
      <!-- Campaign header -->
      <div class="flex items-center gap-2 mb-2">
        <div class="flex items-center gap-1">
          <svg
            class="w-4 h-4 text-green-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
            />
          </svg>
          <span class="text-xs font-semibold text-green-800">
            {{ campaign?.campaignActionName || "Chương trình quà tặng" }}
          </span>
        </div>
        <span
          v-if="campaign?.quantityLimit === 1"
          class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-700 border border-orange-200"
        >
          Chọn 1
        </span>
      </div>

      <!-- Gift items -->
      <div class="space-y-1">
        <div
          v-for="(giftItem, giftIndex) in campaign?.giftPromotions"
          :key="giftItem?.toProductId || giftIndex"
          class="flex items-center gap-2 bg-white rounded-md p-2 border border-green-100"
        >
          <div class="flex-shrink-0">
            <svg
              class="w-3 h-3 text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
              />
            </svg>
          </div>
          <div class="flex-1 min-w-0">
            <div class="text-xs font-medium text-gray-900 truncate">
              {{ giftItem?.productName || "Quà tặng" }}
            </div>
            <div class="text-xs text-green-600">
              Mua {{ giftItem?.fromQuantity || 1 }} tặng
              {{ giftItem?.toQuantity || 1 }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  dataProductGift: any[];
}

defineProps<Props>();
</script>
