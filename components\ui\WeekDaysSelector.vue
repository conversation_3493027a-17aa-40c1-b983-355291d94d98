<template>
  <div>
    <label class="block text-sm text-gray-700 mb-2 font-medium">
      <PERSON>ần hiện tại
    </label>
    <div class="grid grid-cols-7 gap-1">
      <button
        v-for="day in weekDays"
        :key="day.date"
        @click="selectDay(day)"
        class="flex flex-col items-center p-2 rounded-lg text-xs transition-all duration-200 hover:bg-gray-50"
        :class="{
          'bg-primary text-white hover:bg-primary/90': day.isToday,
          'bg-primary/20 text-primary border border-primary':
            !day.isToday && day.isSelected,
          'text-gray-600 hover:bg-gray-50': !day.isToday && !day.isSelected,
        }"
      >
        <span class="font-medium mb-1">{{ day.dayName }}</span>
        <span class="text-lg font-semibold">{{ day.dayNumber }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";

// Props
const props = defineProps<{
  modelValue?: string | null;
}>();

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: string | null];
  daySelected: [day: any];
  dateRangeChanged: [dateRange: any];
}>();

// Reactive data
const selectedDay = ref<string | null>(props.modelValue || null);
const today = new Date();

// Generate week days (Monday to Sunday)
const weekDays = computed(() => {
  const days = [];
  const dayNames = ["T2", "T3", "T4", "T5", "T6", "T7", "CN"];

  // Get current week's Monday
  const currentDate = new Date(today);
  const dayOfWeek = currentDate.getDay();
  const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // Handle Sunday (0) as last day
  const monday = new Date(currentDate);
  monday.setDate(currentDate.getDate() + mondayOffset);

  // Generate 7 days starting from Monday
  for (let i = 0; i < 7; i++) {
    const date = new Date(monday);
    date.setDate(monday.getDate() + i);

    const dateString = date.toISOString().split("T")[0]; // YYYY-MM-DD format
    const isToday = dateString === today.toISOString().split("T")[0];

    days.push({
      date: dateString,
      dayName: dayNames[i],
      dayNumber: date.getDate(),
      fullDate: date,
      isToday,
      isSelected: selectedDay.value === dateString,
    });
  }

  return days;
});

// Functions
const selectDay = (day: any) => {
  selectedDay.value = day.date;

  // Emit v-model update
  emit("update:modelValue", day.date);

  // Emit selected day data
  emit("daySelected", {
    date: day.date,
    fullDate: day.fullDate,
    dayName: day.dayName,
    dayNumber: day.dayNumber,
    isToday: day.isToday,
  });

  // Also emit as date range for compatibility
  const startOfDay = new Date(day.fullDate);
  startOfDay.setHours(0, 0, 0, 0);
  const endOfDay = new Date(day.fullDate);
  endOfDay.setHours(23, 59, 59, 999);

  emit("dateRangeChanged", {
    dateTo: endOfDay.toISOString(),
    dateFrom: startOfDay.toISOString(),
    startDate: startOfDay,
    endDate: endOfDay,
    selectedDay: day.date,
  });
};

// Watch for prop changes
watch(
  () => props.modelValue,
  (newValue) => {
    selectedDay.value = newValue;
  }
);

// Auto-select today on mount
onMounted(() => {
  if (!selectedDay.value) {
    const todayString = today.toISOString().split("T")[0];
    selectedDay.value = todayString;
    emit("update:modelValue", todayString);
  }
});
</script>
