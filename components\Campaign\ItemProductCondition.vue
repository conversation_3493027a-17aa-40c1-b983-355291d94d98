<template>
  <div
    class="bg-white rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200 contain-layout h-full flex flex-col"
  >
    <!-- Product Card -->
    <div class="flex gap-3 p-3 flex-1">
      <!-- Loading State -->
      <div
        v-if="isLoading"
        class="animate-pulse flex items-center gap-3 w-full"
      >
        <div class="w-14 h-14 bg-gray-200 rounded-lg flex-shrink-0"></div>
        <div class="flex-1 space-y-2">
          <div class="h-4 bg-gray-200 rounded w-3/4"></div>
          <div class="h-3 bg-gray-200 rounded w-1/3"></div>
          <div class="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div class="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
      </div>

      <!-- Error State -->
      <div
        v-else-if="error"
        class="flex items-center gap-2 text-red-600 text-sm w-full"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span>Không thể tải thông tin sản phẩm</span>
      </div>

      <!-- Product Content -->
      <template v-else>
        <!-- Product Image -->
        <div class="flex-shrink-0 relative overflow-hidden group">
          <!-- Image Loading Placeholder -->
          <div
            v-if="imageLoading"
            class="w-14 h-14 bg-gray-200 animate-pulse flex items-center justify-center rounded-lg border border-gray-200"
          >
            <svg
              class="w-4 h-4 text-gray-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                clip-rule="evenodd"
              />
            </svg>
          </div>

          <!-- Image Error Fallback -->
          <div
            v-else-if="imageError"
            class="w-14 h-14 bg-gray-100 flex items-center justify-center rounded-lg border border-gray-200"
          >
            <div class="text-center">
              <svg
                class="w-4 h-4 text-gray-400 mx-auto mb-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              <span class="text-xs text-gray-500">Lỗi</span>
            </div>
          </div>

          <!-- Actual Product Image -->
          <NuxtImg
            v-else
            :alt="`Hình ảnh sản phẩm ${dataProduct?.title || 'Sản phẩm'}`"
            :src="handleGetImageProductUrl()"
            :class="imageClasses"
            loading="lazy"
            @load="handleImageLoad"
            @error="handleImageError"
          />

          <!-- Discount badge -->
          <div
            v-if="
              dataProduct?.compareAtPrice &&
              dataProduct?.price < dataProduct?.compareAtPrice
            "
            class="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1 py-0.5 rounded-full font-medium shadow-sm z-10"
          >
            -{{
              calculateDiscountPercent(
                dataProduct.compareAtPrice,
                dataProduct.price
              )
            }}%
          </div>
        </div>

        <!-- Product Info -->
        <div class="flex-1 min-w-0 space-y-2">
          <!-- Product Title -->
          <div class="space-y-1">
            <h3
              class="font-semibold text-gray-900 text-sm leading-5 truncate"
              :title="dataProduct?.title"
            >
              {{ dataProduct?.title || "Tên sản phẩm không có" }}
            </h3>
            <div class="flex items-center gap-2 text-xs text-gray-500">
              <span
                v-if="dataProduct?.id"
                class="inline-flex items-center gap-1"
              >
                <svg
                  class="w-3 h-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                  />
                </svg>
                ID: {{ dataProduct.id }}
              </span>
              <span
                v-if="dataProduct?.sku"
                class="inline-flex items-center gap-1"
              >
                <svg
                  class="w-3 h-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                SKU: {{ dataProduct.sku }}
              </span>
            </div>
          </div>

          <!-- Price Section -->
          <div class="space-y-1">
            <div class="flex items-center gap-2 flex-wrap">
              <div class="font-bold text-red-600 text-base">
                {{
                  dataProduct?.price !== null &&
                  dataProduct?.price !== undefined
                    ? formatCurrency(dataProduct.price)
                    : "Chưa có giá"
                }}
              </div>
              <div
                v-if="dataProduct?.compareAtPrice"
                class="text-sm text-gray-400 line-through"
              >
                {{ formatCurrency(dataProduct.compareAtPrice) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Add to Cart Button -->
        <div class="flex-shrink-0">
          <button
            :disabled="false"
            class="inline-flex items-center gap-1 px-2 py-1 text-xs rounded-md border transition-colors bg-primary text-white border-primary hover:bg-primary/90"
            @click="handleAddProductToOrder"
            title="Thêm vào đơn hàng"
          >
            <svg
              v-if="!isAddingToCart"
              class="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"
              />
            </svg>
            <svg
              v-else
              class="w-3 h-3 animate-spin"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              />
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            <span>{{ isAddingToCart ? "Đang thêm..." : "Thêm" }}</span>
          </button>
        </div>
      </template>
    </div>

    <div
      v-if="product?.giftPromotions?.length"
      class="p-3 pt-0 flex-1 flex flex-col"
    >
      <div class="bg-blue-50 border border-blue-200 rounded-md p-2 mb-3">
        <div class="flex items-center gap-2">
          <svg
            class="w-4 h-4 text-blue-600 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
            />
          </svg>
          <div class="text-xs flex-1 min-w-0">
            <div class="font-medium text-blue-900 truncate">
              {{
                product.quantityLimit === 1
                  ? "Bạn sẽ được tặng 1 trong các sản phẩm sau"
                  : "Bạn sẽ được tặng các sản phẩm sau"
              }}
            </div>
          </div>
        </div>
      </div>

      <div class="space-y-2 flex-1 overflow-y-auto max-h-48">
        <div
          v-for="(giftItem, giftIndex) in product.giftPromotions"
          :key="giftItem?.toProductId || giftIndex"
          class="flex items-center gap-3 p-2 rounded-md border bg-white border-gray-200"
        >
          <div class="flex-shrink-0 relative overflow-hidden group">
            <NuxtImg
              :src="
                getGiftProductImage(giftItem?.toProductId) ||
                'https://placehold.co/40?text=Gift'
              "
              :alt="`Hình ảnh quà tặng ${
                giftItem?.productName || 'Không có tên'
              }`"
              class="object-contain w-10 h-10 rounded-lg border border-gray-200 bg-gray-50 transition-all duration-300 group-hover:scale-105"
              width="40"
              height="40"
              placeholder="Gift product image"
              loading="lazy"
              @error="handleGiftImageError"
            />
            <div
              class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white flex items-center justify-center"
            >
              <svg
                class="w-1.5 h-1.5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
                />
              </svg>
            </div>
          </div>

          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-1">
              <svg
                class="w-3 h-3 text-green-600 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
                />
              </svg>
              <h4
                class="font-semibold text-green-800 text-sm leading-5 truncate"
                :title="giftItem?.productName"
              >
                {{ giftItem?.productName || "Tên sản phẩm quà tặng không có" }}
              </h4>
            </div>

            <div class="flex items-center gap-4 text-xs text-gray-600">
              <div v-if="giftItem?.toProductId" class="flex items-center gap-1">
                <svg
                  class="w-3 h-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                  />
                </svg>
                <span class="font-medium text-gray-500">ID:</span>
                <span
                  class="font-mono bg-gray-100 px-1 py-0.5 rounded text-gray-700"
                >
                  {{ giftItem.toProductId }}
                </span>
              </div>
              <div class="flex items-center gap-1 text-green-600">
                <span class="font-medium">Số lượng:</span>
                <span class="font-semibold">{{
                  giftItem?.toQuantity || 1
                }}</span>
              </div>
              <div
                v-if="giftItem?.fromQuantity"
                class="flex items-center gap-1 text-blue-600"
              >
                <span class="font-medium">Điều kiện:</span>
                <span class="font-semibold"
                  >Mua {{ giftItem.fromQuantity }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-3 text-gray-500 text-sm">
      <svg
        class="w-6 h-6 mx-auto mb-1 text-gray-300"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
        />
      </svg>
      <p>Chưa có sản phẩm tặng kèm</p>
    </div>
  </div>
  <LoadingSpinner v-if="isLoadingSpinner" />
</template>
<script setup lang="ts">
const props = defineProps(["product", "campaign"]);

const { getProductById, getProductVariantById } = useProduct();
const { getImageProducrUrl } = usePortal();
const { formatCurrency } = await import("~/utils/formatCurrency");

const dataProduct = ref<any>(null);
const image = ref<string>("");
const isLoading = ref<boolean>(false);
const error = ref<string | null>(null);
const isAddingToCart = ref<boolean>(false);

const imageLoading = ref<boolean>(true);
const imageError = ref<boolean>(false);

const imageClasses = computed(() => [
  "object-contain w-14 h-14 rounded-lg border border-gray-200 bg-gray-50",
  "transition-all duration-300",
  "group-hover:scale-105",
]);

const handleGetProductDetail = async (): Promise<void> => {
  if (!props.product?.productId) {
    error.value = "Không có ID sản phẩm";
    return;
  }

  try {
    isLoading.value = true;
    error.value = null;

    let response;
    if (props.product.productParentId) {
      response = await getProductVariantById(props.product.productId);
    } else {
      response = await getProductById(props.product.productId);
    }

    if (response) {
      dataProduct.value = response;
    } else {
      throw new Error("Không thể tải thông tin sản phẩm");
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : "Lỗi không xác định";
    dataProduct.value = null;
  } finally {
    isLoading.value = false;
  }
};

const handleGetImageProductUrl = (): string => {
  try {
    return getImageProducrUrl(props.product?.productId, "PRODUCT");
  } catch (error) {
    console.error("Error getting image URL:", error);
    return "https://placehold.co/56?text=No+Image"; // Fallback image
  }
};

const handleImageLoad = (): void => {
  imageLoading.value = false;
  imageError.value = false;
};

const handleImageError = (): void => {
  imageLoading.value = false;
  imageError.value = true;
};

const loadProductImage = (): void => {
  if (props.product?.productId) {
    try {
      const url = getImageProducrUrl(props.product.productId, "PRODUCT");
      image.value = url || "";
    } catch (err) {
      image.value = "";
    }
  }
};

const handleGiftImageError = (event: Event): void => {
  const target = event.target as HTMLImageElement;
  if (target) {
    target.src = "https://placehold.co/40?text=Gift";
  }
};

const getGiftProductImage = (productId: string): string => {
  if (!productId) return "";
  try {
    return getImageProducrUrl(productId, "PRODUCT") || "";
  } catch (err) {
    console.error("Error getting gift product image:", err);
    return "";
  }
};

const calculateDiscountPercent = (
  originalPrice: number,
  salePrice: number
): number => {
  if (!originalPrice || !salePrice || originalPrice <= salePrice) return 0;
  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
};

onMounted(async () => {
  try {
    await Promise.all([handleGetProductDetail(), loadProductImage()]);

    // Preload image - following ProductSimpleCard pattern
    const img = new Image();
    img.onload = handleImageLoad;
    img.onerror = handleImageError;
    img.src = handleGetImageProductUrl();
  } catch (err) {
    console.error("Error during component initialization:", err);
  }
});
const { addProductToOrder } = useOrderStore();
const { addOrderLineItems } = useOrder();
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const isLoadingSpinner = ref(false);
const emit = defineEmits(["cancel"]);
const handleAddProductToOrder = async () => {
  isLoadingSpinner.value = true;
  try {
    const response = await addProductToOrder(dataProduct.value);
    nextTick();
    isLoadingSpinner.value = false;
    emit("cancel");
  } catch (error) {
    isLoadingSpinner.value = false;
  }
};
</script>
