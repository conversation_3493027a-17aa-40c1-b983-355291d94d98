<template>
  <div
    class="campaign-wrapper bg-white border border-gray-100 rounded-lg shadow-sm contain-layout will-change-contents"
  >
    <div v-if="campaign?.length > 0" class="px-2 pt-1">
      <div class="flex items-center gap-2 mb-1">
        <h3 class="font-semibold text-primary text-sm flex items-center gap-1">
          <svg
            class="w-4 h-4 text-primary"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
            />
          </svg>
          Sự kiện khu<PERSON><PERSON>n mãi
        </h3>
        <div class="ml-auto">
          <span
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
          >
            {{ campaign.length }} sự kiện
          </span>
        </div>
      </div>

      <!-- Campaign items with improved styling -->
      <div
        id="campaign-scroll"
        class="flex gap-2 overflow-x-auto scrollbar-thin pb-1"
      >
        <button
          v-for="(item, index) in campaign"
          :key="item?.campaignId"
          @click="selectedCampaign(item)"
          class="group relative inline-flex items-center px-3 py-2 text-xs font-medium rounded-lg border border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 hover:from-blue-50 hover:to-blue-100 hover:text-blue-700 hover:border-blue-200 focus:ring-offset-1 transition-all duration-200 transform hover:shadow-md flex-shrink-0 min-w-[120px]"
        >
          <!-- Campaign icon -->
          <div
            class="flex items-center justify-center w-4 h-4 mr-2 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 group-hover:from-blue-400 group-hover:to-blue-600 transition-all duration-200"
          >
            <!-- PROMOTION_VOUCHER icon -->
            <svg
              v-if="item.type === 'PROMOTION_VOUCHER'"
              class="w-2 h-2 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z"
                clip-rule="evenodd"
              />
            </svg>

            <!-- PRODUCT_PROMOTION icon -->
            <svg
              v-else-if="item.type === 'PROMOTION_PRODUCT'"
              class="w-2 h-2 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM9 9a1 1 0 112 0v4a1 1 0 11-2 0V9z"
                clip-rule="evenodd"
              />
            </svg>

            <!-- Default star icon for other types -->
            <svg
              v-else
              class="w-2 h-2 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
              ></path>
            </svg>
          </div>

          <span class="" :title="item.campaignActionName">
            {{ item.campaignActionName }}
          </span>

          <div
            class="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          ></div>
        </button>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else class="p-6 text-center">
      <div class="flex flex-col items-center justify-center">
        <div
          class="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-3"
        >
          <svg
            class="w-6 h-6 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
        </div>
        <p class="text-sm text-gray-500 font-medium">Không có sự kiện nào</p>
        <p class="text-xs text-gray-400 mt-1">
          Hiện tại chưa có chương trình khuyến mãi
        </p>
      </div>
    </div>
  </div>

  <CampaignMessage
    v-if="isOpenPopup"
    @confirm="handleToogle"
    @cancel="handleToogle"
    @viewDetails="handleViewDetails"
    :campaignInfo="campaignInfo"
  />
</template>

<script setup lang="ts">
const CampaignMessage = defineAsyncComponent(
  () => import("~/components/dialog/CampaignMessage.vue")
);

const orderStore = useOrderStore();
const campaign = computed(() => orderStore.campaign);
const isOpenPopup = ref(false);
const campaignInfo = ref();

const selectedCampaign = async (item: any) => {
  campaignInfo.value = item;
  isOpenPopup.value = true;
};

const handleToogle = () => {
  isOpenPopup.value = !isOpenPopup.value;
};

const handleViewDetails = (campaignInfo: any) => {
  isOpenPopup.value = false;
};
</script>
