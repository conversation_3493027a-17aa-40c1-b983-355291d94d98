<template>
  <div>
    <ProductTreeTable
      :isPageSale="isPageSale"
      :isPageDetailReturnOrder="isPageDetailReturnOrder"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import ProductTreeTable from "./ProductTreeTable.vue";

const props = defineProps(["isPageSale", "isPageDetailReturnOrder"]);
const orderStore = useOrderStore();
const returnStore = returnOrderStore();

// View state
const isTreeView = ref(false);

// Computed properties
const orderDetail = computed(() => orderStore.orderDetail);
const products = computed(
  () => orderStore.orderDetail?.activeOrderItemProfiles
);
const productReturnDetail = computed(
  () => returnStore.orderReturnDetail?.activeOrderItemProfiles
);
const dataListProductDiary = computed(() => orderStore.dataListProductDiary);

// Methods
const toggleView = () => {
  isTreeView.value = !isTreeView.value;
};
</script>
