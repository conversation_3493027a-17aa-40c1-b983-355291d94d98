<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
    @click.self="closeModal"
  >
    <div
      class="bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <div class="bg-primary px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div
              class="flex items-center justify-center w-8 h-8 bg-white/20 rounded-full backdrop-blur-sm"
            >
              <svg
                class="w-4 h-4 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <div>
              <h2 class="text-lg font-semibold text-white">
                Quà tặng kèm sản phẩm
              </h2>
              <p class="text-sm text-white/80 truncate max-w-md">
                {{ productInfo?.title || "Sản phẩm" }}
              </p>
            </div>
          </div>

          <button
            @click="closeModal"
            class="flex items-center justify-center w-8 h-8 text-white/80 hover:text-white hover:bg-white/20 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>
      </div>

      <div class="p-6">
        <div class="h-[60vh] overflow-y-auto">
          <!-- <h3 class="text-lg font-semibold text-gray-900 mb-4">
            Danh sách sản phẩm quà tặng
          </h3> -->

          <div v-if="isLoading" class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div v-for="n in 4" :key="`skeleton-${n}`" class="animate-pulse">
              <div class="flex items-center gap-2 mb-3">
                <div class="w-4 h-4 bg-gray-200 rounded"></div>
                <div class="h-4 bg-gray-200 rounded w-48"></div>
              </div>

              <div
                class="flex gap-3 p-3 bg-white rounded-lg border border-gray-100 shadow-sm"
              >
                <div class="flex-shrink-0 relative">
                  <div class="w-14 h-14 bg-gray-200 rounded-lg"></div>
                  <div
                    class="absolute -top-1 -right-1 w-4 h-4 bg-gray-200 rounded-full"
                  ></div>
                </div>

                <div class="flex-1 min-w-0 space-y-2">
                  <div class="space-y-1">
                    <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-gray-200 rounded"></div>
                      <div class="h-3 bg-gray-200 rounded w-16"></div>
                      <div class="w-3 h-3 bg-gray-200 rounded"></div>
                      <div class="h-3 bg-gray-200 rounded w-20"></div>
                    </div>
                  </div>
                  <div class="space-y-1">
                    <div class="flex items-center gap-2">
                      <div class="h-5 bg-gray-200 rounded w-24"></div>
                      <div class="h-4 bg-gray-200 rounded w-20"></div>
                    </div>
                  </div>
                </div>

                <div class="flex-shrink-0">
                  <div class="h-7 bg-gray-200 rounded-md w-16"></div>
                </div>
              </div>

              <div class="space-y-3 mt-3">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div class="flex items-center gap-2 mb-2">
                    <div class="w-5 h-5 bg-blue-200 rounded"></div>
                    <div class="h-4 bg-blue-200 rounded w-48"></div>
                  </div>
                  <div class="h-3 bg-blue-200 rounded w-64"></div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <div
                    v-for="i in 4"
                    :key="`gift-item-skeleton-${i}`"
                    class="bg-green-50 rounded-lg p-3 border border-green-100 animate-pulse"
                  >
                    <div class="flex items-center gap-3">
                      <div class="flex-shrink-0">
                        <div class="w-5 h-5 bg-green-200 rounded-full"></div>
                      </div>

                      <div class="flex-shrink-0 relative">
                        <div class="w-12 h-12 bg-green-200 rounded-lg"></div>
                        <div
                          class="absolute -top-1 -right-1 w-4 h-4 bg-green-200 rounded-full"
                        ></div>
                      </div>

                      <div class="flex-1 min-w-0 space-y-2">
                        <div class="flex items-center gap-2">
                          <div class="w-3 h-3 bg-green-200 rounded"></div>
                          <div class="h-4 bg-green-200 rounded w-3/4"></div>
                        </div>
                        <div class="h-3 bg-green-200 rounded w-16"></div>
                        <div class="flex items-center gap-4">
                          <div class="h-3 bg-green-200 rounded w-12"></div>
                          <div class="h-3 bg-green-200 rounded w-16"></div>
                        </div>
                      </div>

                      <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-200 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="!isLoading && giftPromotions?.length" class="gap-4">
            <div
              v-for="promotion in giftPromotions"
              :key="promotion.id"
              class="h-fit"
            >
              <div
                class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-3 mb-4"
              >
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <svg
                      class="w-5 h-5 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
                      />
                    </svg>
                    <h4 class="font-semibold text-blue-900">
                      {{
                        promotion?.campaignActionName || "Chương trình quà tặng"
                      }}
                    </h4>
                  </div>

                  <!-- Quantity Limit Badge -->
                  <span
                    v-if="promotion.quantityLimit === 1"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-700 border border-orange-200"
                  >
                    <svg
                      class="w-3 h-3 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    Chọn 1
                  </span>
                </div>
                <p class="text-sm text-blue-700">
                  {{
                    promotion.quantityLimit === 1
                      ? "Bạn sẽ được tặng 1 trong các sản phẩm sau (chỉ được chọn 1 sản phẩm)"
                      : "Bạn sẽ được tặng các sản phẩm sau"
                  }}
                </p>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <ItemCampaignProductGiftSelectable
                  v-for="(giftItem, giftIndex) in promotion.giftPromotions"
                  :key="giftItem?.toProductId || giftIndex"
                  :product="giftItem"
                  :productGift="promotion"
                  :selected="
                    selectedProducts.has(giftItem?.toProductId) ||
                    selectedGiftProduct?.product_id === giftItem?.toProductId
                  "
                  @select="handleSelectGiftProduct"
                  class="transition-all duration-200 hover:shadow-sm cursor-pointer rounded-lg"
                />
              </div>
            </div>
          </div>

          <div
            v-else-if="!isLoading && !giftPromotions?.length"
            class="text-center py-12 text-gray-500"
          >
            <svg
              class="w-16 h-16 mx-auto mb-4 text-gray-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
              />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">
              Không có sản phẩm quà tặng
            </h3>
            <p class="text-sm text-gray-500">
              Sản phẩm này hiện tại không có chương trình quà tặng kèm nào.
            </p>
          </div>

          <div v-else-if="error" class="text-center py-12 text-red-500">
            <svg
              class="w-16 h-16 mx-auto mb-4 text-red-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 class="text-lg font-medium text-red-900 mb-2">
              Lỗi tải dữ liệu
            </h3>
            <p class="text-sm text-red-600 mb-4">{{ error }}</p>
            <button
              @click="fetchGiftPromotions"
              class="inline-flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <svg
                class="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Thử lại
            </button>
          </div>
        </div>
      </div>
    </div>
    <LoadingSpinner v-if="isLoadingAddOrder"></LoadingSpinner>
  </div>
</template>

<script setup lang="ts">
import LoadingSpinner from "../common/LoadingSpinner.vue";

const props = defineProps<{
  isOpen: boolean;
  productInfo: {
    id: string;
    title: string;
    sku: string;
    price: number;
  } | null;
  productImage: string;
}>();

const emit = defineEmits<{
  close: [];
}>();

const isLoading = ref(false);
const error = ref<string | null>(null);
const giftPromotions = ref<any[]>([]);
const selectedGiftProduct = ref<any>(null);
const selectedProducts = ref<Set<string>>(new Set());

const { searchProductGiftPromotionResponse } = useCampaign();

const closeModal = () => {
  // Reset selections when closing modal
  selectedProducts.value.clear();
  selectedGiftProduct.value = null;
  emit("close");
};

// Validation logic can be added here in the future if needed
const { addOrderLineItems } = useOrder();
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const isLoadingAddOrder = ref(false);
const handleSelectGiftProduct = async (productData: any) => {
  isLoadingAddOrder.value = true;
  const dataRequest = [
    {
      quantity: productData?.quantity,
      gift: true,
      product_id: productData?.product_id,
      gift_condition_product_id: productData?.gift_condition_product_id,
      gift_campaign_id: productData?.gift_campaign_id,
      gift_campaign_action_id: productData?.gift_campaign_action_id,
    },
  ];
  await orderStore.addProductGiftToOrder(
    orderDetail.value?.id,
    dataRequest,
    productData?.fromQuantity
  );
  await orderStore.updateOrder(orderDetail.value?.id);
  closeModal();
  isLoadingAddOrder.value = false;
};
const fetchGiftPromotions = async () => {
  if (!props.productInfo?.id) return;

  try {
    isLoading.value = true;
    error.value = null;

    // Reset selections when fetching new data
    selectedProducts.value.clear();
    selectedGiftProduct.value = null;

    const response = await searchProductGiftPromotionResponse(
      [props.productInfo.id],
      "" // campaignActionId - có thể cần điều chỉnh
    );

    giftPromotions.value = response?.content || [];
  } catch (err: any) {
    console.error("Error fetching gift promotions:", err);
    error.value = "Không thể tải thông tin quà tặng. Vui lòng thử lại.";
  } finally {
    isLoading.value = false;
  }
};

// Watch for product changes
watch(
  () => props.productInfo,
  (newProductInfo) => {
    if (newProductInfo && props.isOpen) {
      fetchGiftPromotions();
    }
  },
  { immediate: true }
);

// Watch for modal open/close
watch(
  () => props.isOpen,
  (isOpen) => {
    if (isOpen && props.productInfo) {
      fetchGiftPromotions();
    }
  }
);

// Handle escape key
onMounted(() => {
  const handleEscape = (e: KeyboardEvent) => {
    if (e.key === "Escape" && props.isOpen) {
      closeModal();
    }
  };
  document.addEventListener("keydown", handleEscape);

  onUnmounted(() => {
    document.removeEventListener("keydown", handleEscape);
  });
  console.log("orderDetail", orderDetail.value);
});
</script>
