// Cache for token validation to avoid repeated API calls
const tokenValidationCache = new Map<
  string,
  { isValid: boolean; timestamp: number }
>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export default defineNuxtRouteMiddleware(async (to: any) => {
  const token = useCookie("token") as Ref<string>;

  // Fast path: no token
  if (!token.value) {
    const query: Record<string, string> = {};

    if (to.path !== "/" && to.path !== "/login") {
      query.path = to.path;
    }

    if (to.query.orgId) query.orgId = String(to.query.orgId);
    if (to.query.storeId) query.storeId = String(to.query.storeId);
    if (to.query.orderId) query.orderId = String(to.query.orderId);
    if (to.query.customerId) query.customerId = String(to.query.customerId);
    return navigateTo({ path: "/login", query });
  }
  const cacheKey = token.value;
  const cached = tokenValidationCache.get(cacheKey);
  const now = Date.now();

  if (cached && now - cached.timestamp < CACHE_DURATION) {
    if (!cached.isValid) {
      const { showSessionExpiredModal } = useSessionExpired();
      showSessionExpiredModal(5);
      return;
    }
    return;
  }
  try {
    const { setToken, checkToken } = useAuth();
    const { orgId } = useTabContext();

    setToken(token.value);
    await checkToken(orgId.value, token.value);

    tokenValidationCache.set(cacheKey, { isValid: true, timestamp: now });
  } catch (error) {
    tokenValidationCache.set(cacheKey, { isValid: false, timestamp: now });

    const { showSessionExpiredModal } = useSessionExpired();
    showSessionExpiredModal(5);

    return;
  }
});
