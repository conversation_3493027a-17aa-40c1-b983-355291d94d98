<template>
  <div
    class="flex gap-3 p-3 bg-white rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200 contain-layout"
  >
    <!-- Product Image -->
    <div class="flex-shrink-0 relative overflow-hidden group">
      <NuxtImg
        :alt="`Hình ảnh sản phẩm ${product?.title || 'Sản phẩm'}`"
        :src="handleGetImageProductUrl()"
        :class="imageClasses"
        loading="lazy"
        @load="handleImageLoad"
        @error="handleImageError"
      />

      <!-- Image Loading Placeholder -->
      <div
        v-if="imageLoading"
        class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center rounded-lg"
      >
        <svg
          class="w-4 h-4 text-gray-400"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
            clip-rule="evenodd"
          />
        </svg>
      </div>

      <!-- Image Error Fallback -->
      <div
        v-if="imageError"
        class="absolute inset-0 bg-gray-100 flex items-center justify-center rounded-lg"
      >
        <div class="text-center">
          <svg
            class="w-4 h-4 text-gray-400 mx-auto mb-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <span class="text-xs text-gray-500">Lỗi</span>
        </div>
      </div>

      <!-- Discount badge -->
      <div
        v-if="product.compareAtPrice && product.price < product.compareAtPrice"
        class="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1 py-0.5 rounded-full font-medium shadow-sm z-10"
      >
        -{{ calculateDiscountPercent(product.compareAtPrice, product.price) }}%
      </div>
    </div>

    <!-- Product Info -->
    <div class="flex-1 min-w-0 space-y-2">
      <!-- Product Title -->
      <div class="space-y-1">
        <h3
          class="font-semibold text-gray-900 text-sm leading-5 truncate"
          :title="product.title"
        >
          {{ product.title }}
        </h3>
        <div class="flex items-center gap-2 text-xs text-gray-500">
          <span class="inline-flex items-center gap-1">
            <svg
              class="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
              />
            </svg>
            ID: {{ product?.id }}
          </span>
          <span v-if="product?.sku" class="inline-flex items-center gap-1">
            <svg
              class="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            SKU: {{ product.sku }}
          </span>
        </div>
      </div>

      <!-- Price Section -->
      <div class="space-y-1">
        <div class="flex items-center gap-2 flex-wrap">
          <div class="font-bold text-red-600 text-base">
            {{
              product.price !== null && product.price !== undefined
                ? formatCurrency(product.price)
                : "Chưa có giá"
            }}
          </div>
          <div
            v-if="product.compareAtPrice"
            class="text-sm text-gray-400 line-through"
          >
            {{ formatCurrency(product.compareAtPrice) }}
          </div>
          <!-- Inventory Toggle -->
          <button
            v-if="!showInventory && product?.subType === 'SIMPLE'"
            @click.stop="handleViewInventory"
            class="inline-flex items-center gap-1 px-2 py-1 text-xs text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md border border-blue-200 transition-colors"
          >
            <svg
              class="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
              />
            </svg>
            Xem tồn kho
          </button>
          <div
            v-if="showInventory"
            class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-green-700 bg-green-50 rounded-md border border-green-200"
          >
            <svg
              class="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
              />
            </svg>
            Tồn kho: {{ inventory?.orderAble || 0 }}
          </div>
        </div>

        <!-- VAT Info -->
        <div class="flex items-center gap-4 text-xs">
          <div class="flex items-center gap-1">
            <span class="text-gray-600">VAT:</span>
            <span class="font-medium text-gray-900"
              >{{ product.vat || 0 }}%</span
            >
          </div>
          <div class="flex items-center gap-1">
            <span class="text-gray-600">Giá sau VAT:</span>
            <span class="font-semibold text-gray-900">{{
              formatCurrency(priceAfterVAT)
            }}</span>
          </div>
        </div>
      </div>

      <!-- Promotion Info -->
      <div
        v-if="promotion"
        class="bg-blue-50 border border-blue-200 rounded-md p-2"
      >
        <div class="flex items-center gap-2">
          <svg
            class="w-4 h-4 text-blue-600 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
            />
          </svg>
          <div class="text-xs">
            <div class="font-medium text-blue-900">
              {{ promotion.campaignName }}
            </div>
            <div class="text-blue-700">
              {{ formatTimestampV2(promotion.fromDate) }} -
              {{ formatTimestampV2(promotion.toDate) }}
            </div>
          </div>
        </div>
      </div>
      <!-- thông tin quà tặng -->
      <ProductGiftInfo :dataProductGift="dataProductGift" />
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["product"]);

// Import component
import ProductGiftInfo from "~/components/Product/ProductGiftInfo.vue";

const { getPromotionProductPrice } = useCampaign();
const promotion = ref<{
  campaignName: string;
  fromDate: string;
  toDate: string;
} | null>(null);

// Calculate price after VAT
const priceAfterVAT = computed(() => {
  if (props.product.price === null || props.product.price === undefined) {
    return 0;
  }

  const vatRate = props.product.vat || 0;
  const vatAmount = (props.product.price * vatRate) / 100;
  return props.product.price + vatAmount;
});

const handlePromotionProductPrice = async () => {
  if (props.product?.compareAtPrice) {
    const response = await getPromotionProductPrice(
      props.product.id,
      props.product.price
    );
    promotion.value = response;
  }
};
const { searchProductGiftPromotionResponse } = useCampaign();
const dataProductGift = ref<any>([]);
const handleSearchProductGift = async () => {
  if (props.product?.subType === "VARIABLE") return;
  try {
    const response = await searchProductGiftPromotionResponse(
      [props.product?.id],
      ""
    );
    dataProductGift.value = response?.content;
  } catch (error) {
    throw error;
  }
};

// Image handling - similar to ProductSimpleCard
const { getImageProducrUrl } = usePortal();
const imageLoading = ref(true);
const imageError = ref(false);

// Computed properties for image classes
const imageClasses = computed(() => [
  "object-contain w-14 h-14 rounded-lg border border-gray-200 bg-gray-50",
  "transition-all duration-300",
  "group-hover:scale-105",
  { "opacity-0": imageLoading.value },
]);

// Methods for image handling
const handleGetImageProductUrl = () => {
  try {
    return getImageProducrUrl(props.product.id, "PRODUCT");
  } catch (error) {
    console.error("Error getting image URL:", error);
    return "https://placehold.co/56?text=No+Image"; // Fallback image
  }
};

const handleImageLoad = () => {
  imageLoading.value = false;
  imageError.value = false;
};

const handleImageError = () => {
  imageLoading.value = false;
  imageError.value = true;
};
onMounted(async () => {
  // Preload image
  const img = new Image();
  img.onload = handleImageLoad;
  img.onerror = handleImageError;
  img.src = handleGetImageProductUrl();

  // Load other data
  await Promise.allSettled([
    handlePromotionProductPrice(),
    handleSearchProductGift(),
  ]);
});
const { getInventoryV2 } = useWarehouse();
const warehouseId = useCookie("warehouseId");
const inventory = ref();
const showInventory = ref(false);
const handleViewInventory = async () => {
  showInventory.value = !showInventory.value;
  const data = [
    {
      productId: props.product?.id,
      variantId: "",
      sku: props.product?.sku,
    },
  ];
  const res = await getInventoryV2((warehouseId.value as string) || "", data);
  inventory.value = res[0];
};

// Calculate discount percentage
const calculateDiscountPercent = (
  originalPrice: number,
  salePrice: number
): number => {
  if (!originalPrice || !salePrice || originalPrice <= salePrice) return 0;
  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
};
</script>
