<template>
  <div class=" w-full">
    <div
      v-for="(item, index) in dataCheckin"
      :key="index"
      class="mt-4 border-b"
    >
      <NuxtLink
        :to="`/timekeeping/${item.id}?storeId=${storeId}`"
        @click="handleChooseTimeKeeping(item)"
        class="block"
      >
        <div
          class="bg-white rounded-lg h-auto z-1 p-2  border-primary"
        >
          <div class="font-bold">{{ item?.owner?.name }}</div>
          <div class="flex items-center justify-between">
            <div class="">{{ formatDate(item.createdStamp) }}</div>
            <div :class="handleAction(item.workEffortTypeId)">
              {{ formatAction(item.workEffortTypeId) }}
            </div>
          </div>
        </div>
      </NuxtLink>
    </div>
  </div>
  <ModalEditCheckin v-if="isEditCheckin" @isClose="isClose" />
</template>

<script setup lang="ts">
import { ref } from "vue";
import { format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { vi } from "date-fns/locale";
const storeId = useCookie("storeId").value;
const props = defineProps(["dataCheckin"]);
const isEditCheckin = ref(false);
const timeKeepingStore = useTimeKeepingStore();
const isClose = (value: boolean) => {
  isEditCheckin.value = value;
};
const { getCustomerById } = useCustomer();
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "dd/MM/yyyy", { locale: vi });
};

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "HH:mm", { locale: vi });
};

const formatAction = (value: string) => {
  if (value === "CHECK_IN") {
    return "Vào ca";
  }
  if (value === "CHECK_OUT") {
    return "Hết ca";
  }
  return value;
};

const getActionClass = (value: string) => {
  const baseClass =
    "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium";
  if (value === "CHECK_IN") {
    return `${baseClass} bg-green-100 text-green-800`;
  }
  if (value === "CHECK_OUT") {
    return `${baseClass} bg-red-100 text-red-800`;
  }
  return `${baseClass} bg-gray-100 text-gray-800`;
};

const handleChooseTimeKeeping = (item: any) => {
  timeKeepingStore.setTimeKeeping(item);
};
const handleAction = (value: string) => {
  switch (value) {
    case "CHECK_IN":
      return "text-green-500 text-sm bg-green-100 p-2 px-2 rounded-xl";
    case "CHECK_OUT":
      return "text-red-500 text-sm bg-red-50 p-2 px-2 rounded-xl";
    default:
      return "";
  }
};
</script>
