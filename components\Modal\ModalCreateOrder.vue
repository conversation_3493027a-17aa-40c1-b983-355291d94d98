<template>
  <div>
    <div
      class="fixed inset-0 z-50 flex h-screen items-center justify-center bg-black bg-opacity-50 overflow-y-auto"
      @click.self="handleClosePopup"
    >
      <div
        class="relative inline-block bg-white rounded-lg shadow-lg w-full m-auto xl:w-[700px] lg:w-[50%] max-h-[450px] overflow-hidden"
      >
        <!-- Header -->
        <div
          class="bg-primary text-white px-4 py-3 flex items-center justify-between"
        >
          <div class="flex items-center gap-3">
            <div
              class="flex items-center justify-center w-8 h-8 bg-white/20 rounded-full"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                class="w-5 h-5"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"
                />
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold">Thông báo tồn kho</h2>
              <p class="text-white/80 text-sm">Sản phẩm đã hết hàng</p>
            </div>
          </div>

          <!-- Close Button -->
          <button
            @click="handleClosePopup"
            class="flex items-center justify-center w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-200"
            title="Đóng"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              class="w-5 h-5"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div
          class="overflow-y-auto px-4 py-4"
          style="max-height: calc(450px - 120px)"
        >
          <OrderItemsInventory></OrderItemsInventory>
        </div>

        <div class="flex items-center justify-center gap-3 m-3">
          <div
            @click="handleCreateOrderWithInventory"
            class="flex-1 text-primary rounded px-4 py-2 text-sm border-primary border cursor-pointer text-center"
          >
            Loại bỏ các sản phẩm hết
          </div>
          <div
            @click="handleContinueCreateOrder"
            class="flex-1 bg-primary text-white py-2 rounded px-2 cursor-pointer text-center"
          >
            Tiếp tục tạo đơn
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const emits = defineEmits(["closePopup"]);
const orderStore = useOrderStore();
const handleClosePopup = () => {
  localStorage.setItem("paymentAmount", "0");
  orderStore.isAlert = false;
};
const handleCreateOrderWithInventory = () => {
  orderStore.handleCreateOrderWithInventory();
  // console.log("tao don voi order trong");
};
const handleContinueCreateOrder = () => {
  // console.log("tao don order");
  orderStore.handleContinueCreateOrder();
};
</script>
