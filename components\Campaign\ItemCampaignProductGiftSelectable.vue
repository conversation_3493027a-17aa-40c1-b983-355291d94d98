<template>
  <div
    :class="[
      'bg-green-50 rounded-lg p-3 border transition-all cursor-pointer',
      isChecked
        ? 'border-green-500 bg-green-100 shadow-md'
        : 'border-green-100 hover:bg-green-100 hover:border-green-300',
    ]"
    @click="handleSelect"
  >
    <div class="flex items-center gap-3">
      <div class="flex-shrink-0">
        <div
          :class="[
            'w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all',
            isChecked
              ? 'border-green-500 bg-green-500'
              : 'border-green-300 bg-white hover:border-green-400',
          ]"
        >
          <svg
            v-if="isChecked"
            class="w-3 h-3 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
      </div>

      <div class="flex-shrink-0 relative">
        <NuxtImg
          :src="image || 'https://placehold.co/48'"
          :alt="`Hình ảnh quà tặng ${product?.productName || 'Không có tên'}`"
          class="object-cover w-12 h-12 rounded-lg border border-green-200 transition-transform hover:scale-105"
          width="48"
          height="48"
          placeholder="Gift product image"
          loading="lazy"
          @error="handleImageError"
        />
        <!-- Gift icon badge -->
        <div
          class="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full border-2 border-white flex items-center justify-center"
        >
          <svg
            class="w-2 h-2 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
            />
          </svg>
        </div>
      </div>

      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-2 mb-1">
          <svg
            class="w-3 h-3 text-green-600 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
            />
          </svg>
          <h4
            :class="[
              'font-semibold text-sm leading-5 truncate transition-colors',
              isChecked ? 'text-green-900' : 'text-green-800',
            ]"
            :title="product?.productName"
          >
            {{ product?.productName || "Tên sản phẩm quà tặng không có" }}
          </h4>
        </div>

        <!-- ✅ Quantity Display -->
        <div class="flex items-center gap-2 mb-1" v-if="product?.toQuantity">
          <span class="text-xs text-green-600 font-medium">Số lượng:</span>
          <span
            class="text-xs text-green-800 bg-green-100 px-1.5 py-0.5 rounded"
          >
            {{ product.toQuantity }}
          </span>
        </div>

        <!-- ✅ Compact Gift Product Details -->
        <div class="flex items-center gap-4 text-xs text-green-700">
          <div class="flex items-center gap-1" v-if="product?.toProductId">
            <svg
              class="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
              />
            </svg>
            <span class="font-medium text-green-600">ID:</span>
            <span
              class="font-mono bg-green-100 px-1.5 py-0.5 rounded text-green-800"
            >
              {{ product.toProductId }}
            </span>
          </div>
          <div class="flex items-center gap-1" v-if="product?.sku">
            <svg
              class="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <span class="font-medium text-green-600">SKU:</span>
            <span
              class="font-mono bg-green-100 px-1.5 py-0.5 rounded text-green-800"
            >
              {{ product.sku }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  product?: {
    toProductId?: string;
    productName?: string;
    sku?: string;
    toQuantity?: number;
    fromQuantity?: number;
  };
  selected?: boolean;
  productGift: any;
}

const props = withDefaults(defineProps<Props>(), {
  product: () => ({}),
  productGift: () => [],
  selected: false,
});

const emit = defineEmits<{
  select: [product: any];
}>();

const { getImageProducrUrl } = usePortal();
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);

const image = ref<string>("");

// Check if this product exists in the active order
const isInActiveOrder = computed(() => {
  if (
    !props.product?.toProductId ||
    !orderDetail.value?.activeOrderItemProfiles
  ) {
    return false;
  }

  return orderDetail.value.activeOrderItemProfiles.some((item: any) => {
    return item.orderLineItem?.variant?.id === props.product?.toProductId;
  });
});

// Combined checked state: either manually selected OR exists in active order
const isChecked = computed(() => {
  return props.selected || isInActiveOrder.value;
});

const loadGiftProductImage = (): void => {
  if (props.product?.toProductId) {
    try {
      const url = getImageProducrUrl(props.product.toProductId, "PRODUCT");
      image.value = url || "";
    } catch (err) {
      console.error("Error loading gift product image:", err);
      image.value = "";
    }
  }
};

const handleImageError = (event: Event): void => {
  const target = event.target as HTMLImageElement;
  if (target) {
    target.src = "https://placehold.co/48?text=Gift";
  }
};

const handleSelect = (): void => {
  const res = orderDetail.value.activeOrderItemProfiles.find((item: any) => {
    return item.orderLineItem?.variant?.id === props.productGift?.productId;
  });
  const data = {
    quantity: props.product?.toQuantity,
    gift: true,
    product_id: props.product?.toProductId,
    gift_condition_product_id: res?.id || "",
    gift_campaign_id: props.productGift?.campaignId,
    gift_campaign_action_id: props.productGift?.campaignActionId,
    fromQuantity: props.product?.fromQuantity,
  };
  emit("select", data);
};

onMounted(() => {
  loadGiftProductImage();
});

watch(
  () => props.product?.toProductId,
  (newProductId, oldProductId) => {
    if (newProductId && newProductId !== oldProductId) {
      loadGiftProductImage();
    }
  }
);
</script>
