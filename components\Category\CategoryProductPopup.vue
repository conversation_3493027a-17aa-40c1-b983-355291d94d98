<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
  >
    <div
      class="bg-white rounded-lg shadow-lg px-2 py-4 max-w-sm md:max-w-4xl w-full animate-popup"
    >
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold"><PERSON><PERSON> mụ<PERSON> sản phẩm</div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <!-- phần danh mục -->
      <div class="max-h-[400px] md:h-[400px] h-[250px] overflow-hidden flex">
        <!-- Categories sidebar -->
        <div class="w-1/6 md:w-1/5 overflow-y-auto border-r bg-gray-50">
          <div
            v-for="category in dataCategory"
            @click="selectCategory(category)"
            :key="category?.id"
            :class="{
              'border-b-primary border-b-2 bg-blue-50 text-primary':
                selectedCategory?.id === category?.id,
              'cursor-pointer border-b p-2 hover:bg-gray-100 transition-colors duration-200 text-sm': true,
            }"
          >
            {{ category?.title }}
          </div>
        </div>

        <!-- Products grid -->
        <div class="flex-1 overflow-y-auto border-l bg-white">
          <div class="p-2 h-full">
            <!-- Loading state -->
            <div
              v-if="isLoading"
              class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-x-4 gap-y-2"
            >
              <div v-for="item in 10" :key="`loading-${item}`">
                <LoadingProduct />
              </div>
            </div>

            <!-- Products grid -->
            <div
              v-else
              class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 auto-rows-max gap-x-4 gap-y-2"
            >
              <div v-for="product in dataProduct" :key="product.id">
                <ProductCategoryCard :product="product" />
              </div>
            </div>

            <!-- Empty state -->
            <div
              v-if="!isLoading && dataProduct.length === 0"
              class="flex flex-col items-center justify-center h-full text-gray-500"
            >
              <svg
                class="w-12 h-12 mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-2.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7"
                />
              </svg>
              <p>Không có sản phẩm nào trong danh mục này</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const selectedCategory = ref(); // Thêm biến để lưu trữ category đã chọn

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};

const { getCategory, getProduct } = useProduct();
const dataCategory = ref<any>([]);
const dataProduct = ref<any>([]);

const selectCategory = async (category: any) => {
  isLoading.value = true;
  selectedCategory.value = category;

  try {
    const dataQuery = {
      keyword: "",
      category: category?.id,
    };
    const res = await getProduct(dataQuery);
    dataProduct.value = res.data;
  } catch (error) {
    console.error("Error loading products:", error);
    dataProduct.value = [];
  } finally {
    isLoading.value = false;
  }
};
const isLoading = ref(false);

onMounted(async () => {
  const response = await getCategory("", 1);
  dataCategory.value = response;
  if (dataCategory.value.length > 0) {
    selectCategory(dataCategory.value[0]);
  }
});
</script>
