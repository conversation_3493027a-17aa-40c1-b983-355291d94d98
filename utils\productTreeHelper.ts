/**
 * Product Tree Helper Utilities
 * Functions for building tree structure from order items with gift relationships
 */

export interface TreeNode {
  id: string;
  type: "product" | "gift";
  data: any;
  children: TreeNode[];
  level: number;
  isExpanded?: boolean;
  parentId?: string;
  giftCampaignId?: string;
  giftCampaignActionId?: string;
  giftConditionProductId?: string;
  isDisabled?: boolean; // New property to track if product is disabled (status 90)
}

/**
 * Build tree structure from order items
 * Groups products with their related gift items
 */
export function buildProductTree(orderItems: any[]): TreeNode[] {
  if (!orderItems || orderItems.length === 0) return [];

  const tree: TreeNode[] = [];
  const productMap = new Map<string, TreeNode>();
  const giftItems: any[] = [];
  const regularProducts: any[] = [];

  // Separate regular products and gift items
  orderItems.forEach((item) => {
    const isGift = item.orderLineItem?.customAttribute?.gift === "true";

    if (isGift) {
      giftItems.push(item);
    } else {
      regularProducts.push(item);
    }
  });

  // Create nodes for regular products
  regularProducts.forEach((item) => {
    const productId = item.id;
    const orderStatus = item.orderLineItem?.orderStatus;
    const isDisabled = orderStatus === 90; // Products with status 90 are disabled

    const node: TreeNode = {
      id: productId,
      type: "product",
      data: item,
      children: [],
      level: 0,
      isExpanded: true,
      isDisabled,
    };

    productMap.set(productId, node);
    tree.push(node);
  });

  // Attach gift items to their parent products
  giftItems.forEach((giftItem) => {
    const conditionProductId = giftItem.orderLineItem?.giftConditionProductId;
    const giftCampaignId = giftItem.orderLineItem?.campaignId;
    const giftCampaignActionId = giftItem.orderLineItem?.campaignActionId;

    // Find parent product
    let parentNode: TreeNode | undefined;

    if (conditionProductId) {
      // Find by condition product ID - match with variant ID
      parentNode = productMap.get(conditionProductId);
    }

    // If no specific parent found, try to match by campaign or add to first product
    if (!parentNode && productMap.size > 0) {
      // For now, attach to the first product (can be improved with better logic)
      parentNode = Array.from(productMap.values())[0];
    }

    if (parentNode) {
      const giftOrderStatus = giftItem.orderLineItem?.orderStatus;
      const isGiftDisabled = giftOrderStatus === 90; // Gift items with status 90 are disabled

      const giftNode: TreeNode = {
        id: giftItem.orderLineItem?.variant?.id || giftItem.id,
        type: "gift",
        data: giftItem,
        children: [],
        level: 1,
        parentId: parentNode.id,
        giftCampaignId,
        giftCampaignActionId,
        giftConditionProductId: conditionProductId,
        isDisabled: isGiftDisabled,
      };

      parentNode.children.push(giftNode);
    } else {
      // If no parent found, add as standalone gift item
      const giftOrderStatus = giftItem.orderLineItem?.orderStatus;
      const isGiftDisabled = giftOrderStatus === 90; // Gift items with status 90 are disabled

      const standaloneGiftNode: TreeNode = {
        id: giftItem.orderLineItem?.variant?.id || giftItem.id,
        type: "gift",
        data: giftItem,
        children: [],
        level: 0,
        giftCampaignId,
        giftCampaignActionId,
        giftConditionProductId: conditionProductId,
        isDisabled: isGiftDisabled,
      };

      tree.push(standaloneGiftNode);
    }
  });

  return tree;
}

/**
 * Flatten tree structure for rendering
 * Returns array of nodes with proper indentation levels
 */
export function flattenTree(tree: TreeNode[]): TreeNode[] {
  const result: TreeNode[] = [];

  function traverse(nodes: TreeNode[], level: number = 0) {
    nodes.forEach((node) => {
      node.level = level;
      result.push(node);

      if (node.isExpanded !== false && node.children.length > 0) {
        traverse(node.children, level + 1);
      }
    });
  }

  traverse(tree);
  return result;
}

/**
 * Toggle expand/collapse state of a node
 */
export function toggleNodeExpansion(
  tree: TreeNode[],
  nodeId: string
): TreeNode[] {
  function updateNode(nodes: TreeNode[]): TreeNode[] {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return { ...node, isExpanded: !node.isExpanded };
      }
      if (node.children.length > 0) {
        return { ...node, children: updateNode(node.children) };
      }
      return node;
    });
  }

  return updateNode(tree);
}

/**
 * Get statistics about the tree
 */
export function getTreeStats(tree: TreeNode[]) {
  let productCount = 0;
  let giftCount = 0;

  function count(nodes: TreeNode[]) {
    nodes.forEach((node) => {
      if (node.type === "product") {
        productCount++;
      } else if (node.type === "gift") {
        giftCount++;
      }
      count(node.children);
    });
  }

  count(tree);

  return {
    totalProducts: productCount,
    totalGifts: giftCount,
    totalItems: productCount + giftCount,
  };
}
