<template>
  <div class="rounded-lg py-2 px-1">
    <div class="flex items-center gap-3">
      <!-- Product Image -->
      <div class="flex-shrink-0 relative overflow-hidden group">
        <NuxtImg
          :alt="`<PERSON>ình ảnh sản phẩm ${product?.variant?.title || 'Sản phẩm'}`"
          :src="handleGetImageProductUrl()"
          :class="imageClasses"
          loading="lazy"
          @load="handleImageLoad"
          @error="handleImageError"
        />

        <!-- Gift Badge - Hiển thị khi có gift và giftQuantity là null/undefined -->
        <div
          v-if="isGiftProduct"
          class="absolute -top-[1px] -right-[1px] w-5 h-5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full border-2 border-white flex items-center justify-center shadow-lg"
        >
          <svg
            class="w-3 h-3 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
            />
          </svg>
        </div>

        <!-- Image Loading Placeholder -->
        <div
          v-if="imageLoading"
          class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center rounded-lg"
        >
          <svg
            class="w-4 h-4 text-gray-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clip-rule="evenodd"
            />
          </svg>
        </div>

        <!-- Image Error Fallback -->
        <div
          v-if="imageError"
          class="absolute inset-0 bg-gray-100 flex items-center justify-center rounded-lg"
        >
          <div class="text-center">
            <svg
              class="w-4 h-4 text-gray-400 mx-auto mb-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <span class="text-xs text-gray-500">Lỗi</span>
          </div>
        </div>
      </div>

      <!-- Product Info -->
      <div class="flex-1 min-w-0">
        <!-- Product Title -->
        <h4 class="font-medium text-gray-900 text-sm leading-5 mb-1 truncate">
          {{ product?.variant?.title || "Sản phẩm" }}
        </h4>

        <!-- Product Details -->
        <div>
          <!-- ID & SKU -->
          <div class="flex items-center gap-3 text-xs text-gray-600">
            <div class="flex items-center gap-1">
              <span class="font-medium">ID:</span>
              <span class="font-mono">{{ product?.variant?.id || "N/A" }}</span>
            </div>
            <div class="flex items-center gap-1">
              <span class="font-medium">SKU:</span>
              <span class="font-mono">{{
                product?.variant?.sku || "N/A"
              }}</span>
            </div>
          </div>

          <!-- Quantity × Price | VAT & Total -->
          <div
            class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"
          >
            <!-- Left: Quantity × Price -->
            <div class="flex items-center gap-2 text-sm">
              <span class="font-medium text-gray-700">{{
                product?.quantity || 0
              }}</span>
              <span class="text-gray-400">×</span>
              <span class="">
                {{
                  isItemProductInvoice
                    ? formatCurrency(product?.realPriceSell?.amount)
                    : formatCurrency(product?.variant?.price?.amount)
                }}
              </span>
            </div>

            <!-- Right: VAT & Total -->
            <div class="flex items-center justify-between sm:justify-end gap-2">
              <!-- VAT Info -->
              <div
                v-if="product?.vatRate?.amount"
                class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded flex-shrink-0"
              >
                VAT {{ product?.vatRate?.amount || 0 }}%
              </div>
              <!-- Total Price -->
              <span class="text-sm flex-shrink-0">
                {{
                  formatCurrency(
                    (product?.quantity || 0) *
                      (isItemProductInvoice
                        ? product?.realPriceSell?.amount
                        : product?.variant?.price?.amount)
                  )
                }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Edit Button (if needed) -->
      <div v-if="isItemProductInvoice" class="flex-shrink-0">
        <button
          @click="toogleOpenEditPopup"
          class="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Edit Popup -->
  <EditItemInvoicePopup
    v-if="isOpenEditItemInvoice"
    @cancel="toogleOpenEditPopup"
    @confirm="toogleOpenEditPopup"
  />
</template>

<script setup>
const props = defineProps(["product", "isItemProductInvoice"]);
const emit = defineEmits(["toogleEditItemProductInvoice"]);

// Composables
const { getImageProducrUrl } = usePortal();

// Reactive state
const imageLoading = ref(true);
const imageError = ref(false);
const isOpenEditItemInvoice = ref(false);

// Computed properties
const imageClasses = computed(() => [
  "object-contain w-12 h-12 rounded-lg",
  "transition-all duration-300",
  "group-hover:scale-105",
  { "opacity-0": imageLoading.value },
]);

// Computed property để kiểm tra xem có phải sản phẩm quà tặng không
const isGiftProduct = computed(() => {
  return (
    props.product?.customAttribute?.gift &&
    (props.product?.giftQuantity === null ||
      props.product?.giftQuantity === undefined)
  );
});

// Methods
const handleGetImageProductUrl = () => {
  try {
    return getImageProducrUrl(props.product?.variant?.id, "PRODUCT");
  } catch (error) {
    console.error("Error getting image URL:", error);
    return "https://placehold.co/48?text=No+Image"; // Fallback image
  }
};

const handleImageLoad = () => {
  imageLoading.value = false;
  imageError.value = false;
};

const handleImageError = () => {
  imageLoading.value = false;
  imageError.value = true;
};

const toogleOpenEditPopup = () => {
  isOpenEditItemInvoice.value = !isOpenEditItemInvoice.value;
};

// Lifecycle
onMounted(() => {
  // Preload image
  const img = new Image();
  img.onload = handleImageLoad;
  img.onerror = handleImageError;
  img.src = handleGetImageProductUrl();
});
</script>
