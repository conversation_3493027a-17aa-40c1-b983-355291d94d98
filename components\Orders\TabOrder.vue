<template>
  <div class="bg-white my-2 p-2 relative">
    <div v-if="isLoading" class="flex gap-2 animate-pulse p-1">
      <div
        v-for="item in 5"
        :key="`skeleton-${item}`"
        class="bg-secondary h-6 w-32 rounded"
      ></div>
      <div class="w-6 h-6 bg-secondary rounded"></div>
      <div class="w-6 h-6 bg-secondary rounded"></div>
    </div>

    <div
      v-else
      class="flex gap-2 items-center justify-between overflow-x-auto relative"
    >
      <div class="flex items-center gap-2 relative" ref="tabContainer">
        <div class="flex gap-2">
          <button
            v-for="(order, index) in memoizedListOrder"
            :key="`order-${order.id}`"
            :ref="(el) => setTabRef(el, index)"
            @click="handleClickOrder(order.id, index)"
            :class="getOrderButtonClass(order.id)"
            :disabled="isLoad"
            class="px-2 py-1 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span>
              {{ getOrderDisplayText(order.id) }}
            </span>
          </button>
        </div>

        <!-- Action buttons -->
        <div class="flex gap-2">
          <button
            @click="handleCreateOrderTemp"
            :class="createButtonClass"
            :disabled="isLoad"
            class="p-1 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-6 h-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 4.5v15m7.5-7.5h-15"
              />
            </svg>
          </button>

          <button
            @click="handleReloadListOrder"
            :class="reloadButtonClass"
            :disabled="isLoad"
            class="p-1 rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            :style="{
              transform: isReloading ? 'rotate(360deg)' : 'rotate(0deg)',
            }"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-6 h-6 transition-transform duration-500"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- ActionButtonOrder loading -->
      <Suspense>
        <ActionButtonOrder :diary="orderStore.orderDetail" />
        <template #fallback>
          <!-- Loading skeleton -->
          <div class="flex gap-2 items-center justify-end">
            <div class="w-8 h-8 bg-gray-200 animate-pulse rounded"></div>
            <div class="w-8 h-8 bg-gray-200 animate-pulse rounded"></div>
            <div class="w-8 h-8 bg-gray-200 animate-pulse rounded"></div>
          </div>
        </template>
      </Suspense>
    </div>
  </div>
  <!-- Loading spinner -->
  <Teleport to="body" v-if="loading">
    <LoadingSpinner />
  </Teleport>
</template>

<script setup lang="ts">
// Props and composables
const props = defineProps<{
  isLoad?: boolean;
}>();

const { loading } = useOrder();
const orderStore = useOrderStore();
const route = useRoute();
const router = useRouter();
const tagStore = useTagStore();

// Reactive state
const isLoading = ref(false);
const isReloading = ref(false);
const selectedOrderTerm = ref<string | number>(1);

// Animation state
const underlineLeft = ref(0);
const underlineWidth = ref(0);
const tabContainer = ref<HTMLElement>();
const tabRefs = ref<(HTMLElement | null)[]>([]);

// Removed ripple effect state for performance optimization

// Memoized computed properties for better performance
const memoizedListOrder = computed(() => {
  return orderStore.listOrder || [];
});

// Computed classes using Tailwind CSS
const createButtonClass = computed(
  () =>
    "bg-primary text-white hover:bg-primary-dark focus:ring-2 focus:ring-primary focus:ring-opacity-50"
);

const reloadButtonClass = computed(
  () =>
    "bg-primary text-white hover:bg-primary-dark focus:ring-2 focus:ring-primary focus:ring-opacity-50"
);

// Tab button classes using Tailwind CSS
const getOrderButtonClass = (orderId: string | number) => {
  return selectedOrderTerm.value === orderId
    ? "bg-primary text-white hover:bg-primary-dark focus:ring-2 focus:ring-primary focus:ring-opacity-50"
    : "border border-primary text-primary hover:bg-primary hover:text-white focus:ring-2 focus:ring-primary focus:ring-opacity-50";
};

const getOrderDisplayText = (orderId: string | number) => {
  return isTimestamp(String(orderId)) ? "Đơn mới" : String(orderId);
};

const isTimestamp = (id: string): boolean => {
  return /^\d{13}$/.test(id);
};

// Optimized options object (removed duplicate)
const options = reactive({
  date_create_from: Date.now().toString(),
  currentPage: 1,
  maxResult: 5,
  status: [10],
});

// Animation helper methods
const setTabRef = (el: any, index: number) => {
  if (el && el.$el) {
    tabRefs.value[index] = el.$el;
  } else if (el) {
    tabRefs.value[index] = el;
  }
};

// Optimized underline update with immediate response
const updateUnderlineImmediate = (index: number) => {
  // Use requestAnimationFrame for smooth, non-blocking animation
  requestAnimationFrame(() => {
    const targetTab = tabRefs.value[index];
    if (targetTab && tabContainer.value) {
      const containerRect = tabContainer.value.getBoundingClientRect();
      const tabRect = targetTab.getBoundingClientRect();

      underlineLeft.value = tabRect.left - containerRect.left;
      underlineWidth.value = tabRect.width;
    }
  });
};

// Async version for when nextTick is needed
const updateUnderline = async (index: number) => {
  await nextTick();
  updateUnderlineImmediate(index);
};

// Removed createRippleEffect as it's not being used for performance optimization

// Optimized click handler for instant response
const handleClickOrder = async (orderId: string, index?: number) => {
  if (props.isLoad) return;

  // Immediate UI feedback - no debounce for better responsiveness
  selectedOrderTerm.value = orderId;

  // Non-blocking underline update
  if (typeof index === "number") {
    updateUnderlineImmediate(index);
  }

  // Non-blocking navigation
  // Use tab-isolated context instead of cookies
  const { storeId, orgId } = useTabContext();

  // Use replace instead of push for faster navigation
  router.replace(
    `/sale?orderId=${orderId}&orgId=${orgId.value}&storeId=${storeId.value}`
  );
};

// Optimized updateOrderId function
const updateOrderId = async (orderId: any) => {
  const currentList = memoizedListOrder.value;

  // Reset order state helper
  const resetOrderState = () => {
    orderStore.orderDetail = null;
    tagStore.dataConnector = null;
    orderStore.customerInOrder = null;
    orderStore.dataShippingAddress = null;
    orderStore.dataDefaultAddress = null;
  };

  // If no orderId and list has items, create new temp order
  if (!orderId && currentList?.length > 0) {
    resetOrderState();
    handleCreateOrderTemp();
    return;
  }

  // If no orderId and list is empty, create new temp order
  if (!orderId && !(currentList?.length > 0)) {
    resetOrderState();
    handleCreateOrderTemp();
    return;
  }

  // If orderId exists, find or add to list
  if (orderId) {
    const existingOrder = currentList?.find((item: any) => item.id === orderId);
    if (!existingOrder) {
      orderStore.listOrder.push({ id: orderId });
    }
    selectedOrderTerm.value = orderId;
  }
};

// Optimized create order temp function
const handleCreateOrderTemp = () => {
  const order = Date.now().toString();
  orderStore.handleAddOrderToListOrder(order);
  orderStore.orderDetail = null;
  tagStore.dataConnector = null;
  orderStore.customerInOrder = null;
  handleClickOrder(order);
};

// Enhanced reload function with rotation animation
const handleReloadListOrder = async () => {
  if (isLoading.value || isReloading.value) return; // Prevent multiple simultaneous calls

  try {
    isReloading.value = true;
    isLoading.value = true;

    await orderStore.getListOrder(options);
    await updateOrderId(route.query.orderId);

    // Update underline position after reload
    await nextTick();
    const currentIndex = memoizedListOrder.value.findIndex(
      (order: any) => order.id === selectedOrderTerm.value
    );
    if (currentIndex >= 0) {
      await updateUnderline(currentIndex);
    }
  } catch (error) {
    console.error("Error reloading order list:", error);
  } finally {
    isLoading.value = false;
    // Delay to show rotation animation
    setTimeout(() => {
      isReloading.value = false;
    }, 500);
  }
};

// Optimized watcher with proper cleanup
const stopWatcher = watchEffect(() => {
  if (route.query.orderId) {
    const orderId = Array.isArray(route.query.orderId)
      ? route.query.orderId[0]
      : route.query.orderId;
    selectedOrderTerm.value = orderId || 1;
  }
});

// Optimized watcher with throttling for better performance
const throttledUpdateUnderline = useThrottleFn(async () => {
  await nextTick();
  const currentIndex = memoizedListOrder.value.findIndex(
    (order: any) => order.id === selectedOrderTerm.value
  );
  if (currentIndex >= 0) {
    updateUnderlineImmediate(currentIndex);
  }
}, 16); // ~60fps

// Lightweight watcher for selectedOrderTerm changes
watch(selectedOrderTerm, throttledUpdateUnderline, { flush: "post" });

// Optimized list watcher with shallow comparison
watch(() => memoizedListOrder.value.length, throttledUpdateUnderline, {
  flush: "post",
});

// Combined lifecycle hook with animation initialization
onMounted(async () => {
  try {
    isLoading.value = true;

    // Initialize selected order term
    if (memoizedListOrder.value?.length > 0) {
      selectedOrderTerm.value = memoizedListOrder.value[0]?.id || 1;
    }

    await orderStore.getListOrder(options);
    const orderId = route.query.orderId;
    await updateOrderId(orderId);

    // Initialize underline position after data is loaded
    await nextTick();
    if (memoizedListOrder.value.length > 0) {
      const currentIndex = memoizedListOrder.value.findIndex(
        (order: any) => order.id === selectedOrderTerm.value
      );
      if (currentIndex >= 0) {
        await updateUnderline(currentIndex);
      } else {
        await updateUnderline(0);
      }
    }
  } catch (error) {
    console.error("Error in onMounted:", error);
  } finally {
    isLoading.value = false;
  }
});

// Cleanup on unmount
onUnmounted(() => {
  stopWatcher();
});
</script>


