<template>
  <!-- x - Hidden in tree mode -->
  <td
    v-if="!isTreeNode"
    class="text-center"
    :class="{ 'opacity-50': !isChecked }"
  >
    <input
      type="checkbox"
      id="choose-me"
      v-model="isChecked"
      @click="toogleCheckBox"
      class="w-[15px] h-[15px] accent-primary rounded-full cursor-pointer"
      :disabled="
        isPageDetailReturnOrder ||
        isDisable ||
        orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
          'INVOICE_PUBLISHED'
      "
    />
  </td>
  <!-- hình ảnh -->
  <td class="text-center" :class="{ 'opacity-50': !isChecked }">
    <div class="flex items-center justify-center gap-1">
      <div class="relative overflow-hidden group">
        <NuxtImg
          :alt="`Hình ảnh sản phẩm ${
            product?.orderLineItem?.variant?.title || 'Sản phẩm'
          }`"
          :src="handleGetImageProductUrl()"
          :class="imageClasses"
          loading="lazy"
          @load="handleImageLoad"
          @error="handleImageError"
        />

        <!-- Image Loading Placeholder -->
        <div
          v-if="imageLoading"
          class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center rounded-lg"
        >
          <svg
            class="w-3 h-3 md:w-4 md:h-4 text-gray-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clip-rule="evenodd"
            />
          </svg>
        </div>

        <!-- Image Error Fallback -->
        <div
          v-if="imageError"
          class="absolute inset-0 bg-gray-100 flex items-center justify-center rounded-lg"
        >
          <div class="text-center">
            <svg
              class="w-3 h-3 md:w-4 md:h-4 text-gray-400 mx-auto mb-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <span class="text-xs text-gray-500 hidden md:block">Lỗi</span>
          </div>
        </div>

        <!-- Gift Badge - Hiển thị khi có gift và giftQuantity là null/undefined -->
        <div
          v-if="isGiftProduct"
          class="absolute top-[2px] right-[2px] w-4 h-4 md:w-5 md:h-5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full border-2 border-white flex items-center justify-center shadow-lg"
          v-tippy="giftTooltipContent"
        >
          <svg
            class="w-2 h-2 md:w-3 md:h-3 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
            />
          </svg>
        </div>
      </div>
    </div>
  </td>
  <!-- Tên sản phẩm -->
  <td
    class="py-2 max-w-[50svh]"
    :class="{ 'opacity-50': !isChecked || isProductDisabled }"
  >
    <div class="flex items-center gap-2">
      <div
        class="truncate text-sm font-semibold transition-colors flex items-center gap-1"
        :class="[
          product?.orderLineItem?.customAttribute?.gift === 'true'
            ? ''
            : 'cursor-pointer hover:text-primary',
          isTreeNode && nodeType === 'gift' ? 'text-green-700' : '',
          isProductDisabled ? 'line-through text-gray-500' : '',
        ]"
        @click="openGiftPromotionModal"
        :title="
          'Click để xem quà tặng kèm - ' + product?.orderLineItem?.variant.title
        "
      >
        <span class="truncate">{{
          product?.orderLineItem?.variant.title
        }}</span>
      </div>
    </div>
    <div class="text-sm flex items-center gap-1">
      <div>
        <span class="font-semibold">Id: </span>
        <span>{{ product?.orderLineItem?.variant?.id }}</span>
      </div>
      <div>
        <span class="font-semibold"> - Sku: </span>
        <span>{{ product?.orderLineItem?.variant?.sku }}</span>
      </div>
    </div>
    <div v-if="!isProductDraft" class="flex items-center gap-2">
      <div class="flex items-center gap-1">
        <span class="font-semibold">Kho:</span>
        <span
          v-if="dataProduct?.orderAble > 0"
          class="text-sm line-clamp-2 font-semibold"
        >
          {{ dataProduct?.orderAble }}
        </span>
        <span
          v-else-if="dataProduct?.orderAble <= 0"
          class="text-sm line-clamp-2 font-semibold text-red-500"
        >
          Hết hàng
        </span>
        <span v-else class="text-sm line-clamp-2 font-semibold text-orange-500">
          Chưa lưu kho
        </span>
      </div>
      <div
        @click="handleNavigate"
        class="flex items-center gap-1 text-primary border-b-[1px] border-primary cursor-pointer text-sm"
      >
        <span>Kiểm tra kho</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"
          />
        </svg>
      </div>
    </div>
  </td>
  <!-- Đơn giá -->
  <td
    class="text-center font-semibold"
    :class="[
      { 'opacity-50': !isChecked },
      isGiftProduct ? 'text-orange-600' : 'text-primary',
    ]"
  >
    <div class="space-x-2 flex items-center justify-center">
      <span
        v-if="
          product?.orderLineItem?.variant.price?.amount !==
            product?.orderLineItem?.variant.unitPrice?.amount &&
          targetItem?.isEditPrice
        "
        v-tippy="'Cập nhật về giá gốc'"
        @click="handleResetPrice"
        class="cursor-pointer"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
          />
        </svg>
      </span>
      <span
        v-if="!isOpenEditPrice"
        :class="isGiftProduct ? 'text-orange-600 font-bold' : ''"
      >
        <span
          v-if="
            isGiftProduct && product?.orderLineItem?.variant.price?.amount === 0
          "
          class="flex items-center gap-1"
        >
          <span>{{
            formatCurrency(product?.orderLineItem?.variant.price?.amount)
          }}</span>
        </span>
        <span v-else>
          {{ formatCurrency(product?.orderLineItem?.variant.price?.amount) }}
        </span>
      </span>
      <!--  -->
      <CurrencyInput
        v-else
        class="max-w-[90px] outline-none border p-1 rounded"
        v-model="price"
        :options="{
          currency: 'VND',
          currencyDisplay: 'hidden',
          hideCurrencySymbolOnFocus: false,
          hideGroupingSeparatorOnFocus: false,
          hideNegligibleDecimalDigitsOnFocus: false,
        }"
        @change="handleChangePrice"
      />
      <span
        v-if="targetItem?.isEditPrice"
        v-tippy="'Điều chỉnh giá '"
        class="cursor-pointer"
        @click="toogleOpenEditPrice"
        ><svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
          />
        </svg>
      </span>
    </div>
  </td>

  <!-- Số lượng sản phẩm -->
  <td :class="{ 'opacity-50': !isChecked }" class="p-2">
    <div class="flex items-center space-x-2">
      <button
        @click="handleDecreaseQuantity"
        class="border px-2 rounded"
        :disabled="!isChecked || isPageDetailReturnOrder || isDisable"
      >
        -
      </button>
      <div class="flex items-center gap-1">
        <span>{{ product?.orderLineItem?.currentQuantity }}</span>
      </div>
      <button
        @click="handleIncreaseQuantity"
        class="border px-2 rounded"
        :disabled="!isChecked || isPageDetailReturnOrder || isDisable"
      >
        +
      </button>
      <!-- Gift Quantity Warning Icon -->
      <div
        v-if="hasGiftQuantity"
        class="relative"
        v-tippy="`Số lượng quà tặng: ${product?.orderLineItem?.giftQuantity}`"
      >
        <div class="flex items-center">
          <svg
            class="w-4 h-4 text-orange-500 animate-pulse cursor-help"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
      </div>
    </div>
  </td>

  <!-- Giảm giá -->
  <td
    v-if="isPageSale"
    class="text-center align-middle"
    :class="{ 'opacity-50': !isChecked }"
  >
    <div class="flex items-center justify-center">
      <div
        class="flex items-center justify-center border border-gray-300 bg-secondary rounded px-1 py-1 max-w-[120px]"
      >
        <select
          :disabled="
            !isChecked ||
            isPageDetailReturnOrder ||
            isDisable ||
            orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
              'INVOICE_PUBLISHED'
          "
          class="focus:outline-none bg-transparent"
          v-model="discountType"
          @change="handleDiscountTypeChange"
        >
          <option value="MONEY">₫</option>
          <option value="PERCENT">%</option>
        </select>
        <!-- <input
        :disabled="!isChecked || isPageDetailReturnOrder || isDisable"
        type="text"
        class="w-full focus:outline-none text-right bg-secondary"
        v-model="discountValue"
        @blur="handleBlur"
      /> -->
        <CurrencyInput
          :disabled="
            !isChecked ||
            isPageDetailReturnOrder ||
            isDisable ||
            orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
              'INVOICE_PUBLISHED'
          "
          class="w-full focus:outline-none text-right bg-secondary"
          :key="discountInputKey"
          v-model="discountValue"
          :options="{
            currency: 'VND',
            currencyDisplay: 'hidden',
            hideCurrencySymbolOnFocus: false,
            hideGroupingSeparatorOnFocus: false,
            hideNegligibleDecimalDigitsOnFocus: false,
          }"
          @change="handleBlur"
        />
      </div>
    </div>
  </td>
  <!-- VAT -->
  <!-- <td class="text-center">
    <div v-if="product?.orderLineItem?.vatRate?.amount" class="font-semibold text-primary">
      {{ product?.orderLineItem?.vatRate?.amount }} %
    </div>
  </td> -->
  <!-- Thành tiền -->
  <td
    class="p-2 text-center cursor-pointer"
    :class="{ 'opacity-50': !isChecked }"
  >
    <div class="flex items-center justify-center gap-1">
      <div v-if="product?.orderLineItem?.discountedTotalPrice">
        <div
          :class="
            product?.orderLineItem?.originalTotalPrice?.amount !==
            product?.orderLineItem?.discountedTotalPrice?.amount
              ? ' text-red-400 line-through'
              : 'text-primary font-semibold'
          "
        >
          {{
            formatCurrency(product?.orderLineItem?.originalTotalPrice?.amount)
          }}
        </div>
        <div
          v-if="
            product?.orderLineItem?.originalTotalPrice?.amount !==
            product?.orderLineItem?.discountedTotalPrice?.amount
          "
          class="text-primary font-semibold"
        >
          {{
            formatCurrency(product?.orderLineItem?.discountedTotalPrice?.amount)
          }}
        </div>
      </div>
      <div v-else class="text-primary">
        {{
          formatCurrency(
            product?.orderLineItem?.originalTotalPrice?.amount +
              (product?.orderLineItem?.totalVAT?.amount || 0)
          )
        }}
      </div>
    </div>
    <div class="text-xs" v-if="product?.orderLineItem?.vatRate">
      <span
        ref="vatTooltipRef"
        v-tippy="vatTooltipContent"
        class="cursor-pointer"
      >
        {{ `(VAT${product?.orderLineItem?.vatRate?.amount || 0}%)` }}
      </span>
    </div>
  </td>
  <EditPricePopup
    v-if="isOpenReasonEditPrice"
    @confirm="handleConfirmEditPrice"
    @cancel="handleCancelEditPopup"
  ></EditPricePopup>
  <LoadingSpinner v-if="isLoading"></LoadingSpinner>
  <!-- Gift Promotion Modal -->
  <ProductGiftPromotionModal
    v-if="isGiftPromotionModalOpen"
    :is-open="isGiftPromotionModalOpen"
    :product-info="modalProductInfo"
    :product-image="modalProductImage"
    @close="closeGiftPromotionModal"
  />
</template>
<script setup lang="ts">
const ProductGiftPromotionModal = defineAsyncComponent(
  () => import("~/components/dialog/ProductGiftPromotionModal.vue")
);
const EditPricePopup = defineAsyncComponent(
  () => import("~/components/dialog/EditPricePopup.vue")
);
const props = defineProps([
  "product",
  "isPageSale",
  "isProductDraft",
  "isPageDetailReturnOrder",
  "isDisable",
  "isTreeNode",
  "nodeType",
  "nodeLevel",
  "isProductDisabled",
]);
const isChecked = ref(props.isProductDraft ? false : true);
const isLoading = ref(false);
const orderStore = useOrderStore();
const { disableOrderItem, enableProductDiary, updatePriceNewInOrder } =
  useOrder();
const orderDetail = computed(() => orderStore.orderDetail);

// Computed property để kiểm tra xem có phải sản phẩm quà tặng không
const isGiftProduct = computed(() => {
  return (
    props.product?.orderLineItem?.customAttribute?.gift === "true" &&
    (props.product?.orderLineItem?.giftQuantity === null ||
      props.product?.orderLineItem?.giftQuantity === undefined)
  );
});

// Computed property cho gift tooltip content
const giftTooltipContent = computed(() => {
  if (!isGiftProduct.value) return "";

  const conditionProductId =
    props.product?.orderLineItem?.giftConditionProductId;
  const campaignId = props.product?.orderLineItem?.campaignId;
  const campaignActionId = props.product?.orderLineItem?.campaignActionId;

  let tooltip = "Sản phẩm quà tặng";
  if (conditionProductId) {
    tooltip += `\nĐiều kiện: Mua sản phẩm ID ${conditionProductId}`;
  }
  if (campaignId) {
    tooltip += `\nCampagne: ${campaignId}`;
  }
  if (campaignActionId) {
    tooltip += `\nAction: ${campaignActionId}`;
  }

  return tooltip;
});

// Computed property để kiểm tra xem có gift quantity không
const hasGiftQuantity = computed(() => {
  return (
    props.product?.orderLineItem?.customAttribute?.gift &&
    props.product?.orderLineItem?.giftQuantity
  );
});

// watch(
//   () => isChecked.value,
//   async (newVal, oldVal) => {
//     if (newVal) {
//       await enableProductDiary(
//         orderDetail.value.id,
//         props.product?.orderLineItem?.id
//       );
//       await orderStore.getOrderById(orderDetail.value.id);
//     } else {
//       await disableOrderItem(
//         orderDetail.value.id,
//         props.product?.orderLineItem?.id
//       );
//       await orderStore.getOrderById(orderDetail.value.id);
//     }
//   }
// );
//
const toogleCheckBox = async () => {
  if (isChecked.value) {
    const res = await disableOrderItem(
      orderDetail.value.id,
      props.product?.orderLineItem?.id
    );
    if (res?.status === 1) {
      await orderStore.getOrderById(orderDetail.value.id);
    } else {
      isChecked.value = true;
    }
  } else {
    const res = await enableProductDiary(
      orderDetail.value.id,
      props.product?.orderLineItem?.id
    );
    if (res?.status === 1) {
      await orderStore.getOrderById(orderDetail.value.id);
    } else {
      isChecked.value = false;
    }
  }
};
//
// const handleChangeCheckbox = async () => {
//   if (isChecked.value) {
//     const response = await disableOrderItem(
//       orderDetail.value.id,
//       props.product?.orderLineItem?.id
//     );
//     await orderStore.getOrderById(orderDetail.value.id);
//   } else {
//     await enableProductDiary(
//       orderDetail.value.id,
//       props.product?.orderLineItem?.id
//     );
//     await orderStore.getOrderById(orderDetail.value.id);
//   }
// };
//
const isUpdating = ref<Boolean>(false);
const handleIncreaseQuantity = async () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  isLoading.value = true;
  const newQuantity = props.product.orderLineItem?.currentQuantity + 1;
  await orderStore.updateQuantity(props.product.orderLineItem?.id, newQuantity);
  isLoading.value = false;
};
const handleDecreaseQuantity = async () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }

  if (props.product.orderLineItem?.currentQuantity > 1) {
    const newQuantity = props.product.orderLineItem?.currentQuantity - 1;
    isLoading.value = true;
    await orderStore.updateQuantity(
      props.product.orderLineItem?.id,
      newQuantity
    );
    isLoading.value = false;
  } else {
    useNuxtApp().$toast.warning(
      "Sản phẩm có số lượng nhỏ hơn hoặc bằng 1 nên không thể giảm"
    );
  }
  // console.log("đang giảm số lượng sản phẩm");
};
const dataProduct = ref();
const productStore = useProductStore();
const discountValue = ref(
  props.product?.orderLineItem?.discount?.value?.amount
);

// Image handling - similar to ProductSimpleCard
const { getImageProducrUrl } = usePortal();
const imageLoading = ref(true);
const imageError = ref(false);

// Computed properties for image classes
const imageClasses = computed(() => [
  "w-8 h-8 md:w-14 md:h-14 rounded-lg border border-gray-200 bg-gray-50",
  "object-contain transition-all duration-300",
  "group-hover:scale-105",
  { "opacity-0": imageLoading.value },
]);

// Methods for image handling
const handleGetImageProductUrl = () => {
  try {
    return getImageProducrUrl(
      props.product?.orderLineItem?.variant?.id,
      "PRODUCT"
    );
  } catch (error) {
    console.error("Error getting image URL:", error);
    return "https://placehold.co/56?text=No+Image"; // Fallback image
  }
};

const handleImageLoad = () => {
  imageLoading.value = false;
  imageError.value = false;
};

const handleImageError = () => {
  imageLoading.value = false;
  imageError.value = true;
};
const { data } = await useFetch<any>("/data/setting.json");
const route = useRoute();
const targetItem = computed(() =>
  data.value?.find((item: any) => item.storeId === route.query.orgId)
);
onMounted(async () => {
  // Preload image
  const img = new Image();
  img.onload = handleImageLoad;
  img.onerror = handleImageError;
  img.src = handleGetImageProductUrl();

  price.value = props.product.orderLineItem.variant.price.amount;
  if (!props.isProductDraft) {
    dataProduct.value = await orderStore.handleCheckInventory(
      props.product?.orderLineItem
    );
  }

  if (props.product?.orderLineItem?.discount?.value?.amount) {
    discountValue.value = props.product?.orderLineItem?.discount?.value?.amount;
  } else {
    discountValue.value = 0;
  }
});
const discountType = ref("MONEY");
const isEditOrderTypeDisCount = ref(false);

const { updateDiscountPriceInOrder } = useOrder();
const discountInputKey = ref(0);

const handleDiscountTypeChange = async () => {
  discountValue.value = 0;
  discountInputKey.value++;
  const data = {
    type_discount: discountType.value,
    discount_amount: discountValue.value,
    campaign_id: "",
    campaign_action_id: "",
    campaign_action_type: "",
  };

  await updateDiscountPriceInOrder(
    props.product?.orderLineItem?.order_id,
    props.product?.orderLineItem?.id,
    data
  );
  await orderStore.updateOrder(props.product?.orderLineItem?.order_id);
  discountInputKey.value++;
};
const isEditOrderDisCount = ref(false);
const price = ref();
const handleBlur = async () => {
  discountInputKey.value++;

  if (discountType.value === "PERCENT" && discountValue.value > 100) {
    discountValue.value = 100; // Đặt giá trị tối đa là 100
    // app.$toast.warning("Chiết khấu phần trăm không thể lớn hơn 100%");
  }

  if (
    discountType.value === "MONEY" &&
    discountValue.value > props.product?.orderLineItem.originalTotalPrice.amount
  ) {
    discountValue.value =
      props.product?.orderLineItem.originalTotalPrice.amount; // Đặt giá trị tối đa là giá sản phẩm
    // app.$toast.warning("Chiết khấu tiền không thể lớn hơn giá sản phẩm");
  }

  if (discountValue.value >= 0) {
    discountInputKey.value++;

    const data = {
      type_discount: discountType.value,
      discount_amount: discountValue.value,
      campaign_id: "",
      campaign_action_id: "",
      campaign_action_type: "",
    };
    await updateDiscountPriceInOrder(
      props.product?.orderLineItem?.order_id,
      props.product?.orderLineItem?.id,
      data
    );
    await orderStore.updateOrder(props.product?.orderLineItem?.order_id);
    discountValue.value = props.product?.orderLineItem?.discount?.value?.amount;
    discountInputKey.value++;
    discountType.value = "MONEY";
  }
};

watch(
  () => props.product,
  async (newProduct) => {
    if (newProduct) {
      // Reload image when product changes
      imageLoading.value = true;
      imageError.value = false;
      const img = new Image();
      img.onload = handleImageLoad;
      img.onerror = handleImageError;
      img.src = handleGetImageProductUrl();

      price.value = newProduct?.orderLineItem?.variant?.price?.amount;
      if (!props.isProductDraft) {
        dataProduct.value = await orderStore.handleCheckInventory(
          newProduct?.orderLineItem
        );
      }
      discountValue.value =
        newProduct?.orderLineItem?.discount?.value?.amount || 0;
    }
  },
  { immediate: true }
);
const { getUrlWarehousePortal } = usePortal();
const handleNavigate = () => {
  const url = getUrlWarehousePortal(
    props.product.orderLineItem?.variant?.sku,
    props.product?.orderLineItem?.variant?.id
  );
  if (url) {
    window.open(url, "_blank");
  }
};
const handleChangePrice = async () => {
  tooglePopupReasonEditPrice();
};
const isOpenEditPrice = ref(false);
const toogleOpenEditPrice = () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  isOpenEditPrice.value = !isOpenEditPrice.value;
};
const handleResetPrice = async () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  await updatePriceNewInOrder(
    props.product?.orderLineItem?.order_id,
    props.product?.orderLineItem?.id,
    props.product.orderLineItem.variant.unitPrice?.amount,
    "Cập nhật lại giá bán sản phẩm ban đầu"
  );
  await orderStore.getOrderById(props.product?.orderLineItem?.order_id);
  isOpenEditPrice.value = false;
};
const isOpenReasonEditPrice = ref(false);
const handleConfirmEditPrice = async (reason: string) => {
  tooglePopupReasonEditPrice();
  await updatePriceNewInOrder(
    props.product?.orderLineItem?.order_id,
    props.product?.orderLineItem?.id,
    price.value,
    reason
  );
  await orderStore.getOrderById(props.product?.orderLineItem?.order_id);
  toogleOpenEditPrice();
};
const tooglePopupReasonEditPrice = () => {
  isOpenReasonEditPrice.value = !isOpenReasonEditPrice.value;
};
const handleCancelEditPopup = async () => {
  tooglePopupReasonEditPrice();
  toogleOpenEditPrice();
  price.value = props.product.orderLineItem.variant.price.amount;
};

// Computed property cho VAT tooltip content
const vatTooltipContent = computed(() => {
  const realPriceSell =
    props.product?.orderLineItem?.realPriceSell?.amount || 0;
  const totalVAT = props.product?.orderLineItem?.totalVAT?.amount || 0;
  const totalPrice =
    realPriceSell * props.product?.orderLineItem?.currentQuantity + totalVAT;
  return formatCurrency(totalPrice) + " (Giá sau giảm + VAT)";
});

// Ref cho tooltip element
const vatTooltipRef = ref<any | null>(null);

// Gift Promotion Modal state
const isGiftPromotionModalOpen = ref(false);
const modalProductInfo = computed(() => {
  if (!props.product?.orderLineItem?.variant) return null;

  return {
    id: props.product.orderLineItem.variant.id,
    title: props.product.orderLineItem.variant.title,
    sku: props.product.orderLineItem.variant.sku,
    price: props.product.orderLineItem.variant.price?.amount || 0,
  };
});

const modalProductImage = computed(() => {
  return handleGetImageProductUrl();
});

// Gift Promotion Modal methods
const openGiftPromotionModal = () => {
  if (
    props.product?.orderLineItem?.customAttribute?.gift === "true" ||
    props.product?.orderLineItem?.orderStatus === 90
  ) {
    return;
  }
  isGiftPromotionModalOpen.value = true;
};

const closeGiftPromotionModal = () => {
  isGiftPromotionModalOpen.value = false;
};

// Watch để cập nhật tooltip khi data thay đổi
watch(
  () => props.product,
  (newProduct) => {
    if (newProduct && vatTooltipRef.value?._tippy) {
      vatTooltipRef.value._tippy.setContent(vatTooltipContent.value);
    }
  },
  { deep: true }
);
</script>
