import type { Config } from "tailwindcss";

export default <Partial<Config>>{
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./nuxt.config.{js,ts}",
    "./app.vue",
  ],
  theme: {
    container: {
      center: true,
      padding: "1rem",
    },
    extend: {
      colors: {
        primary: {
          light: "#0D47A1",
          DEFAULT: process.env.PRIMARY_COLOR || "#0D47A1",
          dark: "#0D47A1",
        },
        secondary: {
          light: "#f8f9fa",
          DEFAULT: process.env.SECONDARY_COLOR || "#F1F2F5",
          dark: "#eaecf0",
        },
        myChat: {
          light: "#E3F2FD",
          DEFAULT: process.env.SECONDARY_COLOR || "#E3F2FD",
          dark: "#E3F2FD",
        },
      },
      screens: {
        "2xl": "1400px",
        xs: "300px",
      },
      height: {
        "screen-50": "calc(100vh - 70px)",
        "screen-200": "calc(100vh - 170px)",
        "screen-150": "calc(100vh - 140px)",
        "screen-175": "calc(100vh - 175px)",
        "screen-40": "calc(100vh - 65px)",
        "screen-100": "calc(100vh - 122px)",
        "screen-110": "calc(100vh - 110px)",
        "screen-80": "calc(100vh - 80px)",
      },
      maxHeight: {
        "screen-50": "calc(100vh - 80px)",
        "screen-150": "calc(100vh - 270px)",
      },
      animation: {
        popup: "popupAnimation 0.2s ease-in-out forwards",
        closeModal: "closeModal 0.5s ease-in-out forwards",
        slideUp: "slideUp 0.2s ease-in-out forwards", // Thêm animation mới
      },
      keyframes: {
        popupAnimation: {
          "0%": { transform: "scale(0.5)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
        closeModal: {
          "0%": { transform: "scale(1)", opacity: "1" },
          "100%": { transform: "scale(0.5)", opacity: "0" },
        },
        slideUp: {
          "0%": { transform: "translateY(5px)" },
          "100%": { transform: "translateY(-4px)" },
        },
      },
    },
  },
  plugins: [
    // Plugin for thin scrollbars with enhanced styling
    function ({ addBase, theme }: any) {
      addBase({
        // Webkit browsers (Chrome, Safari, Edge)
        "::-webkit-scrollbar": {
          width: "6px",
          height: "6px",
        },
        "::-webkit-scrollbar-track": {
          background: "#f1f5f9",
          borderRadius: "3px",
        },
        "::-webkit-scrollbar-thumb": {
          background: "#cbd5e1",
          borderRadius: "3px",
          transition: "background-color 0.2s ease",
          "&:hover": {
            background: "#94a3b8",
          },
          "&:active": {
            background: "#64748b",
          },
        },
        "::-webkit-scrollbar-corner": {
          background: "#f1f5f9",
        },
        // Firefox
        "*": {
          scrollbarWidth: "thin",
          scrollbarColor: "#cbd5e1 #f1f5f9",
        },
        // For better compatibility
        html: {
          scrollbarWidth: "thin",
          scrollbarColor: "#cbd5e1 #f1f5f9",
        },
        // Mobile responsive scrollbars
        "@media (max-width: 768px)": {
          "::-webkit-scrollbar": {
            width: "4px",
            height: "4px",
          },
        },
      });
    },
  ],
};
