<template>
  <!-- ✅ Optimized ItemCampaignProductGift Component -->
  <div
    class="bg-green-50 rounded-lg p-3 border border-green-100 transition-all hover:bg-green-100"
  >
    <div class="flex items-center gap-3">
      <!-- ✅ Enhanced Gift Product Image -->
      <div class="flex-shrink-0 relative">
        <NuxtImg
          :src="image || 'https://placehold.co/48'"
          :alt="`Hình ảnh quà tặng ${product?.productName || 'Không có tên'}`"
          class="object-cover w-12 h-12 rounded-lg border border-green-200 transition-transform hover:scale-105"
          width="48"
          height="48"
          placeholder="Gift product image"
          loading="lazy"
          @error="handleImageError"
        />
        <!-- Gift Badge -->
        <div
          class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center"
        >
          <svg
            class="w-2 h-2 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
      </div>

      <!-- ✅ Enhanced Gift Product Info -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-2 mb-1">
          <!-- Gift Icon -->
          <svg
            class="w-3 h-3 text-green-600 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
            />
          </svg>
          <h4
            class="font-semibold text-green-800 text-sm leading-5 truncate"
            :title="product?.productName"
          >
            {{ product?.productName || "Tên sản phẩm quà tặng không có" }}
          </h4>
        </div>

        <!-- ✅ Compact Gift Product Details -->
        <div class="flex items-center gap-4 text-xs text-green-700">
          <div class="flex items-center gap-1" v-if="product?.toProductId">
            <span class="font-medium text-green-600">ID:</span>
            <span
              class="font-mono bg-green-100 px-1.5 py-0.5 rounded text-green-800"
            >
              {{ product.toProductId }}
            </span>
          </div>
          <div class="flex items-center gap-1" v-if="product?.sku">
            <span class="font-medium text-green-600">SKU:</span>
            <span
              class="font-mono bg-green-100 px-1.5 py-0.5 rounded text-green-800"
            >
              {{ product.sku }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// ✅ Optimized ItemCampaignProductGift Script with TypeScript

// Props with better typing
interface Props {
  product?: {
    toProductId?: string;
    productName?: string;
    sku?: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  product: () => ({}),
});

// ✅ Composables
const { getImageProducrUrl } = usePortal();

// ✅ Reactive State
const image = ref<string>("");

// ✅ Enhanced Image Loading with Error Handling
const loadGiftProductImage = (): void => {
  if (props.product?.toProductId) {
    try {
      const url = getImageProducrUrl(props.product.toProductId, "PRODUCT");
      image.value = url || "";
    } catch (err) {
      console.error("Error loading gift product image:", err);
      image.value = "";
    }
  }
};

// ✅ Image Error Handler
const handleImageError = (event: Event): void => {
  const target = event.target as HTMLImageElement;
  if (target) {
    target.src = "https://placehold.co/48?text=Gift";
  }
};

// ✅ Lifecycle
onMounted(() => {
  loadGiftProductImage();
});

// ✅ Watch for prop changes
watch(
  () => props.product?.toProductId,
  (newProductId, oldProductId) => {
    if (newProductId && newProductId !== oldProductId) {
      loadGiftProductImage();
    }
  }
);
</script>
