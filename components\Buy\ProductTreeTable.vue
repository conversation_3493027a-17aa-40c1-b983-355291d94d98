<template>
  <div>
    <!-- Table -->
    <div class="overflow-auto max-h-[60svh]">
      <table class="w-full border-collapse">
        <thead>
          <tr class="text-center text-sm bg-gray-100 sticky top-0 z-1">
            <th class="w-1/24 font-bold justify-center items-center flex py-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                />
              </svg>
            </th>
            <th class="w-1/12">Ảnh</th>
            <th :class="isPageDetailReturnOrder ? 'w-5/12' : 'w-4/12'">
              Tên sản phẩm
            </th>
            <th class="w-2/12 text-center">Đơn giá</th>
            <th class="w-1/12">Số lượng</th>
            <th v-if="isPageSale" class="w-2/12 text-center">Giảm giá</th>
            <th class="w-2/12">
              <div class="inline-flex items-center gap-1">
                <span>Thành tiền</span>
                <span v-tippy="'Chưa bao gồm VAT'" class="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="w-4 h-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
                    />
                  </svg>
                </span>
              </div>
            </th>
          </tr>
        </thead>

        <tbody v-if="!isPageDetailReturnOrder">
          <tr
            v-for="node in flattenedTree"
            :key="node.id"
            :class="[
              'border-b transition-colors',
              node.isDisabled
                ? 'bg-gray-100 hover:bg-gray-150 opacity-60'
                : node.type === 'gift'
                ? 'bg-green-50 hover:bg-green-100'
                : 'bg-white hover:bg-gray-50',
            ]"
          >
            <ProductTreeRow
              :node="node"
              :isPageSale="isPageSale"
              :isDisable="orderDetail?.status === 'CANCELLED'"
              @toggle-expansion="handleToggleExpansion"
            />
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import {
  buildProductTree,
  flattenTree,
  toggleNodeExpansion,
  getTreeStats,
  type TreeNode,
} from "~/utils/productTreeHelper";
import ProductTreeRow from "./ProductTreeRow.vue";

const props = defineProps<{
  isPageSale?: boolean;
  isPageDetailReturnOrder?: boolean;
}>();

const orderStore = useOrderStore();
const returnStore = returnOrderStore();

const orderDetail = computed(() => orderStore.orderDetail);
const products = computed(
  () => orderStore.orderDetail?.orderItemProfiles || []
);

// Tree state
const productTree = ref<TreeNode[]>([]);
const allExpanded = ref(true);

// Build tree from products
const buildTree = () => {
  // Use all orderItemProfiles which includes both enabled and disabled products
  const allItems = products.value;
  productTree.value = buildProductTree(allItems);
};

// Computed properties
const flattenedTree = computed(() => flattenTree(productTree.value));
const treeStats = computed(() => getTreeStats(productTree.value));

// Methods
const handleToggleExpansion = (nodeId: string) => {
  productTree.value = toggleNodeExpansion(productTree.value, nodeId);
};

const toggleAllExpansion = () => {
  allExpanded.value = !allExpanded.value;

  const updateAllNodes = (nodes: TreeNode[]): TreeNode[] => {
    return nodes.map((node) => ({
      ...node,
      isExpanded: allExpanded.value,
      children: updateAllNodes(node.children),
    }));
  };

  productTree.value = updateAllNodes(productTree.value);
};

// Watch for data changes
watch(products, buildTree, {
  immediate: true,
  deep: true,
});
</script>
